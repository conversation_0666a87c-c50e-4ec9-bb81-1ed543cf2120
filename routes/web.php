<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Public Pages
Route::get('/', function () {
    return Inertia::render('public/home');
})->name('home');

Route::get('/about', function () {
    return Inertia::render('public/about');
})->name('about');

Route::get('/services', function () {
    return Inertia::render('public/services');
})->name('services');

Route::get('/services/{slug}', function ($slug) {
    return Inertia::render('public/service-detail', ['slug' => $slug]);
})->name('service.show');

Route::get('/team', function () {
    return Inertia::render('public/team');
})->name('team');

Route::get('/blog', function () {
    return Inertia::render('public/blog');
})->name('blog');

Route::get('/blog/{slug}', function ($slug) {
    return Inertia::render('public/blog-post', ['slug' => $slug]);
})->name('blog.show');

Route::get('/contact', function () {
    return Inertia::render('public/contact');
})->name('contact');

Route::get('/book-consultation', function () {
    return Inertia::render('public/book-consultation');
})->name('consultation.book');

// Legacy welcome route for compatibility
Route::get('/welcome', function () {
    return Inertia::render('welcome');
})->name('welcome');

// Protected Pages
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
