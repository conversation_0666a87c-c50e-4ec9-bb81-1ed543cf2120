<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class EnsureUserHasRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$roles): BaseResponse
    {
        if (!auth()->check()) {
            return $this->unauthorizedResponse($request, 'Authentication required');
        }

        $user = auth()->user();
        
        if (!$user->role || !in_array($user->role, $roles)) {
            return $this->forbiddenResponse($request, 'Insufficient permissions');
        }

        return $next($request);
    }

    /**
     * Return unauthorized response based on request type
     */
    private function unauthorizedResponse(Request $request, string $message): BaseResponse
    {
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $message,
            ], 401);
        }

        return redirect()->guest(route('login'));
    }

    /**
     * Return forbidden response based on request type
     */
    private function forbiddenResponse(Request $request, string $message): BaseResponse
    {
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $message,
            ], 403);
        }

        abort(403, $message);
    }
}
