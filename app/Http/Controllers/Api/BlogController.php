<?php

namespace App\Http\Controllers\Api;

use App\Services\BlogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BlogController extends BaseApiController
{
    public function __construct(
        private BlogService $blogService
    ) {}

    /**
     * Get published blog posts
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->get('search'),
                'category_id' => $request->get('category_id'),
                'sort_by' => $request->get('sort_by', 'published_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $posts = $this->blogService->getPublishedPosts($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($posts, 'Blog posts retrieved successfully');
            }

            return $this->successResponse($posts, 'Blog posts retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve blog posts');
        }
    }

    /**
     * Get blog post by slug
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $post = $this->blogService->findBySlug($slug);

            if (!$post) {
                return $this->errorResponse('Blog post not found', 404);
            }

            // Increment views count
            $this->blogService->incrementViews($post->id);

            // Get related posts
            $relatedPosts = $this->blogService->getRelatedPosts($post, 3);

            $response = [
                'post' => $post,
                'related_posts' => $relatedPosts,
            ];

            return $this->successResponse($response, 'Blog post retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve blog post');
        }
    }

    /**
     * Get blog categories
     */
    public function categories(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->get('search'),
            ];

            $categories = $this->blogService->getCategories($filters);

            return $this->successResponse($categories, 'Categories retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve categories');
        }
    }
}
