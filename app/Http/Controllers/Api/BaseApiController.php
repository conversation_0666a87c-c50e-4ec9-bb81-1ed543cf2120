<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class BaseApiController extends Controller
{
    /**
     * Default pagination limit
     */
    protected int $defaultLimit = 15;

    /**
     * Maximum pagination limit
     */
    protected int $maxLimit = 100;

    /**
     * Return a success response
     */
    protected function successResponse(mixed $data = null, string $message = 'Success', int $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return an error response
     */
    protected function errorResponse(string $message = 'Error', int $statusCode = 400, array $errors = []): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a paginated response
     */
    protected function paginatedResponse(LengthAwarePaginator $paginator, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $paginator->items(),
            'pagination' => [
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
                'has_more_pages' => $paginator->hasMorePages(),
            ],
        ]);
    }

    /**
     * Get pagination parameters from request
     */
    protected function getPaginationParams(Request $request): array
    {
        $limit = (int) $request->get('limit', $this->defaultLimit);
        $limit = min($limit, $this->maxLimit);
        $limit = max($limit, 1);

        return [
            'limit' => $limit,
            'page' => (int) $request->get('page', 1),
        ];
    }

    /**
     * Get search parameters from request
     */
    protected function getSearchParams(Request $request): array
    {
        return [
            'search' => $request->get('search'),
            'sort_by' => $request->get('sort_by', 'created_at'),
            'sort_direction' => $request->get('sort_direction', 'desc'),
        ];
    }

    /**
     * Handle validation exceptions
     */
    protected function handleValidationException(ValidationException $e): JsonResponse
    {
        return $this->errorResponse(
            'Validation failed',
            422,
            $e->errors()
        );
    }

    /**
     * Handle general exceptions
     */
    protected function handleException(\Exception $e, string $context = ''): JsonResponse
    {
        Log::error('API Exception: ' . $e->getMessage(), [
            'context' => $context,
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
        ]);

        if (app()->environment('production')) {
            return $this->errorResponse('An error occurred while processing your request', 500);
        }

        return $this->errorResponse($e->getMessage(), 500);
    }

    /**
     * Validate required fields
     */
    protected function validateRequired(Request $request, array $rules): array
    {
        return $request->validate($rules);
    }

    /**
     * Check if user has required role
     */
    protected function checkRole(string $role): bool
    {
        return auth()->check() && auth()->user()->role === $role;
    }

    /**
     * Check if user has any of the required roles
     */
    protected function checkRoles(array $roles): bool
    {
        return auth()->check() && in_array(auth()->user()->role, $roles);
    }

    /**
     * Get authenticated user or fail
     */
    protected function getAuthenticatedUser()
    {
        if (!auth()->check()) {
            abort(401, 'Unauthorized');
        }

        return auth()->user();
    }

    /**
     * Apply search filters to query
     */
    protected function applySearchFilters($query, array $searchParams, array $searchableFields = [])
    {
        if (!empty($searchParams['search']) && !empty($searchableFields)) {
            $search = $searchParams['search'];
            $query->where(function ($q) use ($search, $searchableFields) {
                foreach ($searchableFields as $field) {
                    $q->orWhere($field, 'LIKE', "%{$search}%");
                }
            });
        }

        return $query;
    }

    /**
     * Apply sorting to query
     */
    protected function applySorting($query, array $searchParams, array $sortableFields = [])
    {
        $sortBy = $searchParams['sort_by'] ?? 'created_at';
        $sortDirection = $searchParams['sort_direction'] ?? 'desc';

        // Validate sort field
        if (!empty($sortableFields) && !in_array($sortBy, $sortableFields)) {
            $sortBy = 'created_at';
        }

        // Validate sort direction
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }

        return $query->orderBy($sortBy, $sortDirection);
    }

    /**
     * Log API activity
     */
    protected function logActivity(string $action, array $data = []): void
    {
        Log::info('API Activity', [
            'action' => $action,
            'user_id' => auth()->id(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'data' => $data,
        ]);
    }
}
