<?php

namespace App\Http\Controllers\Api;

use App\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PaymentController extends BaseApiController
{
    public function __construct(
        private PaymentService $paymentService
    ) {}

    /**
     * Get user's payments
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser();
            
            $filters = [
                'status' => $request->get('status'),
                'payment_method' => $request->get('payment_method'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $payments = $this->paymentService->getUserPayments($user->id, $filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($payments, 'Payments retrieved successfully');
            }

            return $this->successResponse($payments, 'Payments retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve payments');
        }
    }

    /**
     * Get payment details
     */
    public function show(int $id): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser();
            $payment = $this->paymentService->findByIdOrFail($id);

            // Check if user owns this payment
            if ($payment->user_id !== $user->id) {
                return $this->errorResponse('Unauthorized', 403);
            }

            return $this->successResponse(
                $payment->load(['user', 'consultation.service']),
                'Payment retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve payment');
        }
    }

    /**
     * Handle Paddle webhook
     */
    public function paddleWebhook(Request $request): JsonResponse
    {
        try {
            $webhookData = $request->all();
            
            $this->logActivity('paddle_webhook_received', [
                'event_type' => $webhookData['event_type'] ?? 'unknown',
                'data_id' => $webhookData['data']['id'] ?? null,
            ]);

            $processed = $this->paymentService->processPaddleWebhook($webhookData);

            if ($processed) {
                return $this->successResponse(null, 'Webhook processed successfully');
            } else {
                return $this->errorResponse('Failed to process webhook', 400);
            }
        } catch (\Exception $e) {
            return $this->handleException($e, 'Webhook processing failed');
        }
    }

    /**
     * Handle CoinBase webhook
     */
    public function coinbaseWebhook(Request $request): JsonResponse
    {
        try {
            $webhookData = $request->all();
            
            $this->logActivity('coinbase_webhook_received', [
                'event_type' => $webhookData['event']['type'] ?? 'unknown',
                'data_id' => $webhookData['event']['data']['id'] ?? null,
            ]);

            $processed = $this->paymentService->processCoinBaseWebhook($webhookData);

            if ($processed) {
                return $this->successResponse(null, 'Webhook processed successfully');
            } else {
                return $this->errorResponse('Failed to process webhook', 400);
            }
        } catch (\Exception $e) {
            return $this->handleException($e, 'Webhook processing failed');
        }
    }
}
