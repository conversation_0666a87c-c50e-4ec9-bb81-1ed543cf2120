<?php

namespace App\Http\Controllers\Api;

use App\Services\AnalyticsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AnalyticsController extends BaseApiController
{
    public function __construct(
        private AnalyticsService $analyticsService
    ) {}

    /**
     * Track analytics event (client-side)
     */
    public function track(Request $request): JsonResponse
    {
        try {
            $event = $this->analyticsService->trackEvent($request->all());

            // Don't log this activity to avoid infinite loops
            return $this->successResponse(
                ['event_id' => $event->id],
                'Event tracked successfully',
                201
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to track event');
        }
    }
}
