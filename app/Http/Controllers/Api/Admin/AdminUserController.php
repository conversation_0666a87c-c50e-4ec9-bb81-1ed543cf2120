<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\BaseApiController;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AdminUserController extends BaseApiController
{
    public function index(Request $request): JsonResponse
    {
        try {
            $query = User::query();

            // Apply filters
            if ($request->has('role')) {
                $query->where('role', $request->get('role'));
            }

            if ($request->has('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('email', 'LIKE', "%{$search}%")
                      ->orWhere('company', 'LIKE', "%{$search}%");
                });
            }

            // Apply sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortDirection = $request->get('sort_direction', 'desc');
            $query->orderBy($sortBy, $sortDirection);

            // Paginate
            $limit = min($request->get('limit', 15), 100);
            $users = $query->paginate($limit);

            return $this->paginatedResponse($users, 'Users retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve users');
        }
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'password' => 'required|string|min:8',
                'role' => 'required|in:admin,client,team_member',
                'phone' => 'nullable|string|max:20',
                'company' => 'nullable|string|max:255',
                'avatar' => 'nullable|string|max:255',
            ]);

            $validated['password'] = Hash::make($validated['password']);
            $validated['email_verified_at'] = now();

            $user = User::create($validated);

            $this->logActivity('user_created', [
                'user_id' => $user->id,
                'email' => $user->email,
                'role' => $user->role,
            ]);

            return $this->successResponse($user, 'User created successfully', 201);
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to create user');
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $user = User::findOrFail($id);
            return $this->successResponse($user, 'User retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve user');
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $user = User::findOrFail($id);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'email' => 'sometimes|required|email|unique:users,email,' . $id,
                'password' => 'sometimes|nullable|string|min:8',
                'role' => 'sometimes|required|in:admin,client,team_member',
                'phone' => 'sometimes|nullable|string|max:20',
                'company' => 'sometimes|nullable|string|max:255',
                'avatar' => 'sometimes|nullable|string|max:255',
            ]);

            if (isset($validated['password'])) {
                $validated['password'] = Hash::make($validated['password']);
            }

            $user->update($validated);

            $this->logActivity('user_updated', [
                'user_id' => $user->id,
                'email' => $user->email,
            ]);

            return $this->successResponse($user->refresh(), 'User updated successfully');
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update user');
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            $user = User::findOrFail($id);

            // Prevent deleting the current admin user
            if ($user->id === auth()->id()) {
                return $this->errorResponse('Cannot delete your own account', 400);
            }

            $user->delete();

            $this->logActivity('user_deleted', [
                'user_id' => $id,
                'email' => $user->email,
            ]);

            return $this->successResponse(null, 'User deleted successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to delete user');
        }
    }
}
