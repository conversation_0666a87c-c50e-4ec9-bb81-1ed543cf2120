<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\BaseApiController;
use App\Services\BlogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AdminBlogController extends BaseApiController
{
    public function __construct(
        private BlogService $blogService
    ) {}

    /**
     * Get all blog posts (admin)
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->get('search'),
                'category_id' => $request->get('category_id'),
                'author_id' => $request->get('author_id'),
                'status' => $request->get('status'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $posts = $this->blogService->getAll($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($posts, 'Blog posts retrieved successfully');
            }

            return $this->successResponse($posts, 'Blog posts retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve blog posts');
        }
    }

    /**
     * Create blog post
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $post = $this->blogService->create($request->all());

            $this->logActivity('blog_post_created', [
                'post_id' => $post->id,
                'title' => $post->title,
                'status' => $post->status,
            ]);

            return $this->successResponse(
                $post->load(['category', 'author']),
                'Blog post created successfully',
                201
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to create blog post');
        }
    }

    /**
     * Get blog post details
     */
    public function show(int $id): JsonResponse
    {
        try {
            $post = $this->blogService->findByIdOrFail($id);

            return $this->successResponse(
                $post->load(['category', 'author', 'comments.user']),
                'Blog post retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve blog post');
        }
    }

    /**
     * Update blog post
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $post = $this->blogService->update($id, $request->all());

            $this->logActivity('blog_post_updated', [
                'post_id' => $post->id,
                'title' => $post->title,
                'status' => $post->status,
            ]);

            return $this->successResponse(
                $post->load(['category', 'author']),
                'Blog post updated successfully'
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update blog post');
        }
    }

    /**
     * Delete blog post
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $post = $this->blogService->findByIdOrFail($id);
            $deleted = $this->blogService->delete($id);

            if ($deleted) {
                $this->logActivity('blog_post_deleted', [
                    'post_id' => $id,
                    'title' => $post->title,
                ]);

                return $this->successResponse(null, 'Blog post deleted successfully');
            }

            return $this->errorResponse('Failed to delete blog post', 500);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to delete blog post');
        }
    }

    /**
     * Get categories (for blog management)
     */
    public function categories(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->get('search'),
            ];

            $categories = $this->blogService->getCategories($filters);

            return $this->successResponse($categories, 'Categories retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve categories');
        }
    }

    /**
     * Create category
     */
    public function storeCategory(Request $request): JsonResponse
    {
        try {
            $category = $this->blogService->createCategory($request->all());

            $this->logActivity('blog_category_created', [
                'category_id' => $category->id,
                'name' => $category->name,
            ]);

            return $this->successResponse(
                $category,
                'Category created successfully',
                201
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to create category');
        }
    }

    /**
     * Update category
     */
    public function updateCategory(Request $request, int $id): JsonResponse
    {
        try {
            $category = $this->blogService->updateCategory($id, $request->all());

            $this->logActivity('blog_category_updated', [
                'category_id' => $category->id,
                'name' => $category->name,
            ]);

            return $this->successResponse($category, 'Category updated successfully');
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update category');
        }
    }

    /**
     * Delete category
     */
    public function destroyCategory(int $id): JsonResponse
    {
        try {
            $deleted = $this->blogService->deleteCategory($id);

            if ($deleted) {
                $this->logActivity('blog_category_deleted', [
                    'category_id' => $id,
                ]);

                return $this->successResponse(null, 'Category deleted successfully');
            }

            return $this->errorResponse('Failed to delete category', 500);
        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), 'existing blog posts')) {
                return $this->errorResponse($e->getMessage(), 409);
            }
            return $this->handleException($e, 'Failed to delete category');
        }
    }
}
