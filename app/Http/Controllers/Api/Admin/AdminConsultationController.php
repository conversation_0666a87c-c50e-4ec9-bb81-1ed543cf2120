<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\BaseApiController;
use App\Services\ConsultationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AdminConsultationController extends BaseApiController
{
    public function __construct(
        private ConsultationService $consultationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'status' => $request->get('status'),
                'payment_status' => $request->get('payment_status'),
                'service_id' => $request->get('service_id'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'consultation_date'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $consultations = $this->consultationService->getAllConsultations($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($consultations, 'Consultations retrieved successfully');
            }

            return $this->successResponse($consultations, 'Consultations retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve consultations');
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $consultation = $this->consultationService->findByIdOrFail($id);

            return $this->successResponse(
                $consultation->load(['service', 'user']),
                'Consultation retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve consultation');
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $consultation = $this->consultationService->update($id, $request->all());

            $this->logActivity('consultation_updated_admin', [
                'consultation_id' => $consultation->id,
                'updated_by' => auth()->id(),
            ]);

            return $this->successResponse(
                $consultation->load(['service', 'user']),
                'Consultation updated successfully'
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update consultation');
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            $consultation = $this->consultationService->findByIdOrFail($id);
            $deleted = $this->consultationService->delete($id);

            if ($deleted) {
                $this->logActivity('consultation_deleted', [
                    'consultation_id' => $id,
                    'deleted_by' => auth()->id(),
                ]);

                return $this->successResponse(null, 'Consultation deleted successfully');
            }

            return $this->errorResponse('Failed to delete consultation', 500);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to delete consultation');
        }
    }
}
