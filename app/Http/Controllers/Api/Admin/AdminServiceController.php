<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\BaseApiController;
use App\Services\ServiceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AdminServiceController extends BaseApiController
{
    public function __construct(
        private ServiceService $serviceService
    ) {}

    /**
     * Get all services (admin)
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->get('search'),
                'category' => $request->get('category'),
                'is_active' => $request->get('is_active'),
                'sort_by' => $request->get('sort_by', 'sort_order'),
                'sort_direction' => $request->get('sort_direction', 'asc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $services = $this->serviceService->getAll($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($services, 'Services retrieved successfully');
            }

            return $this->successResponse($services, 'Services retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve services');
        }
    }

    /**
     * Create service
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $service = $this->serviceService->create($request->all());

            $this->logActivity('service_created', [
                'service_id' => $service->id,
                'title' => $service->title,
            ]);

            return $this->successResponse(
                $service,
                'Service created successfully',
                201
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to create service');
        }
    }

    /**
     * Get service details
     */
    public function show(int $id): JsonResponse
    {
        try {
            $service = $this->serviceService->findByIdOrFail($id);

            return $this->successResponse($service, 'Service retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve service');
        }
    }

    /**
     * Update service
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $service = $this->serviceService->update($id, $request->all());

            $this->logActivity('service_updated', [
                'service_id' => $service->id,
                'title' => $service->title,
            ]);

            return $this->successResponse($service, 'Service updated successfully');
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update service');
        }
    }

    /**
     * Delete service
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $service = $this->serviceService->findByIdOrFail($id);
            $deleted = $this->serviceService->delete($id);

            if ($deleted) {
                $this->logActivity('service_deleted', [
                    'service_id' => $id,
                    'title' => $service->title,
                ]);

                return $this->successResponse(null, 'Service deleted successfully');
            }

            return $this->errorResponse('Failed to delete service', 500);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to delete service');
        }
    }
}
