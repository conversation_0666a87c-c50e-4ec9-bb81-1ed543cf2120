<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\BaseApiController;
use App\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminPaymentController extends BaseApiController
{
    public function __construct(
        private PaymentService $paymentService
    ) {}

    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'status' => $request->get('status'),
                'payment_method' => $request->get('payment_method'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $payments = $this->paymentService->getAllPayments($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($payments, 'Payments retrieved successfully');
            }

            return $this->successResponse($payments, 'Payments retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve payments');
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $payment = $this->paymentService->findByIdOrFail($id);

            return $this->successResponse(
                $payment->load(['user', 'consultation.service']),
                'Payment retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve payment');
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $payment = $this->paymentService->update($id, $request->all());

            $this->logActivity('payment_updated_admin', [
                'payment_id' => $payment->id,
                'updated_by' => auth()->id(),
            ]);

            return $this->successResponse(
                $payment->load(['user', 'consultation.service']),
                'Payment updated successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update payment');
        }
    }
}
