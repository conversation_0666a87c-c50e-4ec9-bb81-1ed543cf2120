<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\BaseApiController;
use App\Services\TeamService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AdminTeamController extends BaseApiController
{
    public function __construct(
        private TeamService $teamService
    ) {}

    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->get('search'),
                'is_active' => $request->get('is_active'),
                'user_id' => $request->get('user_id'),
                'sort_by' => $request->get('sort_by', 'sort_order'),
                'sort_direction' => $request->get('sort_direction', 'asc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $teamMembers = $this->teamService->getAllWithUsers($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($teamMembers, 'Team members retrieved successfully');
            }

            return $this->successResponse($teamMembers, 'Team members retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve team members');
        }
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $teamMember = $this->teamService->create($request->all());

            $this->logActivity('team_member_created', [
                'team_member_id' => $teamMember->id,
                'name' => $teamMember->name,
            ]);

            return $this->successResponse(
                $teamMember->load('user'),
                'Team member created successfully',
                201
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to create team member');
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $teamMember = $this->teamService->findByIdOrFail($id);

            return $this->successResponse(
                $teamMember->load('user'),
                'Team member retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve team member');
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $teamMember = $this->teamService->update($id, $request->all());

            $this->logActivity('team_member_updated', [
                'team_member_id' => $teamMember->id,
                'name' => $teamMember->name,
            ]);

            return $this->successResponse(
                $teamMember->load('user'),
                'Team member updated successfully'
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update team member');
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            $teamMember = $this->teamService->findByIdOrFail($id);
            $deleted = $this->teamService->delete($id);

            if ($deleted) {
                $this->logActivity('team_member_deleted', [
                    'team_member_id' => $id,
                    'name' => $teamMember->name,
                ]);

                return $this->successResponse(null, 'Team member deleted successfully');
            }

            return $this->errorResponse('Failed to delete team member', 500);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to delete team member');
        }
    }

    public function updateSortOrder(Request $request): JsonResponse
    {
        try {
            $sortData = $request->get('sort_data', []);
            $updated = $this->teamService->updateSortOrder($sortData);

            if ($updated) {
                $this->logActivity('team_sort_order_updated', [
                    'items_count' => count($sortData),
                ]);

                return $this->successResponse(null, 'Sort order updated successfully');
            }

            return $this->errorResponse('Failed to update sort order', 500);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update sort order');
        }
    }
}
