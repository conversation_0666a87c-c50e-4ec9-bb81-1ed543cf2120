<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\BaseApiController;
use App\Services\AnalyticsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AdminAnalyticsController extends BaseApiController
{
    public function __construct(
        private AnalyticsService $analyticsService
    ) {}

    public function dashboard(Request $request): JsonResponse
    {
        try {
            $filters = [
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
            ];

            $dashboardData = $this->analyticsService->getDashboardData($filters);

            return $this->successResponse($dashboardData, 'Dashboard data retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve dashboard data');
        }
    }

    public function events(Request $request): JsonResponse
    {
        try {
            $filters = [
                'event_name' => $request->get('event_name'),
                'user_id' => $request->get('user_id'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $events = $this->analyticsService->getEvents($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($events, 'Analytics events retrieved successfully');
            }

            return $this->successResponse($events, 'Analytics events retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve analytics events');
        }
    }

    public function performance(Request $request): JsonResponse
    {
        try {
            $filters = [
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
            ];

            $performance = $this->analyticsService->getTrackingPerformance($filters);

            return $this->successResponse($performance, 'Tracking performance retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve tracking performance');
        }
    }

    public function updateSettings(Request $request): JsonResponse
    {
        try {
            $settings = $request->all();
            $updated = $this->analyticsService->updateTrackingSettings($settings);

            if ($updated) {
                $this->logActivity('tracking_settings_updated', [
                    'settings_count' => count($settings),
                    'updated_by' => auth()->id(),
                ]);

                return $this->successResponse(null, 'Tracking settings updated successfully');
            }

            return $this->errorResponse('Failed to update tracking settings', 500);
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update tracking settings');
        }
    }

    public function getSettings(): JsonResponse
    {
        try {
            $settings = $this->analyticsService->getTrackingSettings();

            return $this->successResponse($settings, 'Tracking settings retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve tracking settings');
        }
    }
}
