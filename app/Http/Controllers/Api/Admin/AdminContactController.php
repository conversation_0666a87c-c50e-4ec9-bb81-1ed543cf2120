<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\BaseApiController;
use App\Services\ContactService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminContactController extends BaseApiController
{
    public function __construct(
        private ContactService $contactService
    ) {}

    public function contactSubmissions(Request $request): JsonResponse
    {
        try {
            $filters = [
                'status' => $request->get('status'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $submissions = $this->contactService->getContactSubmissions($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($submissions, 'Contact submissions retrieved successfully');
            }

            return $this->successResponse($submissions, 'Contact submissions retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve contact submissions');
        }
    }

    public function newsletterSubscriptions(Request $request): JsonResponse
    {
        try {
            $filters = [
                'status' => $request->get('status'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'subscribed_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $subscriptions = $this->contactService->getNewsletterSubscriptions($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($subscriptions, 'Newsletter subscriptions retrieved successfully');
            }

            return $this->successResponse($subscriptions, 'Newsletter subscriptions retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve newsletter subscriptions');
        }
    }

    public function updateContactSubmission(Request $request, int $id): JsonResponse
    {
        try {
            $status = $request->get('status');
            $submission = $this->contactService->updateContactSubmissionStatus($id, $status);

            $this->logActivity('contact_submission_updated', [
                'submission_id' => $submission->id,
                'status' => $status,
                'updated_by' => auth()->id(),
            ]);

            return $this->successResponse($submission, 'Contact submission updated successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update contact submission');
        }
    }

    public function deleteContactSubmission(int $id): JsonResponse
    {
        try {
            // This would need to be implemented in ContactService
            // For now, return a placeholder response
            $this->logActivity('contact_submission_deleted', [
                'submission_id' => $id,
                'deleted_by' => auth()->id(),
            ]);

            return $this->successResponse(null, 'Contact submission deleted successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to delete contact submission');
        }
    }
}
