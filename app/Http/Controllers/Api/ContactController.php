<?php

namespace App\Http\Controllers\Api;

use App\Services\ContactService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class ContactController extends BaseApiController
{
    public function __construct(
        private ContactService $contactService
    ) {}

    /**
     * Submit contact form
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $submission = $this->contactService->submitContactForm($request->all());

            $this->logActivity('contact_form_submitted', [
                'submission_id' => $submission->id,
                'email' => $submission->email,
            ]);

            return $this->successResponse(
                ['id' => $submission->id],
                'Contact form submitted successfully',
                201
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to submit contact form');
        }
    }

    /**
     * Subscribe to newsletter
     */
    public function newsletter(Request $request): JsonResponse
    {
        try {
            $subscription = $this->contactService->subscribeToNewsletter($request->all());

            $this->logActivity('newsletter_subscribed', [
                'subscription_id' => $subscription->id,
                'email' => $subscription->email,
            ]);

            return $this->successResponse(
                ['id' => $subscription->id],
                'Successfully subscribed to newsletter',
                201
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), 'already subscribed')) {
                return $this->errorResponse($e->getMessage(), 409);
            }
            return $this->handleException($e, 'Failed to subscribe to newsletter');
        }
    }

    /**
     * Get user profile (authenticated)
     */
    public function profile(): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser();
            $profile = $this->contactService->getUserProfile($user->id);

            return $this->successResponse($profile, 'Profile retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve profile');
        }
    }

    /**
     * Update user profile (authenticated)
     */
    public function updateProfile(Request $request): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser();
            $updatedUser = $this->contactService->updateUserProfile($user->id, $request->all());

            $this->logActivity('profile_updated', [
                'user_id' => $updatedUser->id,
            ]);

            return $this->successResponse($updatedUser, 'Profile updated successfully');
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update profile');
        }
    }
}
