<?php

namespace App\Http\Controllers\Api;

use App\Services\TeamService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TeamController extends BaseApiController
{
    public function __construct(
        private TeamService $teamService
    ) {}

    /**
     * Get active team members
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'sort_order'),
                'sort_direction' => $request->get('sort_direction', 'asc'),
                'paginate' => $request->boolean('paginate', false),
                ...$this->getPaginationParams($request),
            ];

            $teamMembers = $this->teamService->getActiveMembers($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($teamMembers, 'Team members retrieved successfully');
            }

            return $this->successResponse($teamMembers, 'Team members retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve team members');
        }
    }
}
