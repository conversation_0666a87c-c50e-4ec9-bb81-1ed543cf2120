<?php

namespace App\Http\Controllers\Api;

use App\Services\ServiceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ServiceController extends BaseApiController
{
    public function __construct(
        private ServiceService $serviceService
    ) {}

    /**
     * Get all active services
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->get('search'),
                'category' => $request->get('category'),
                'sort_by' => $request->get('sort_by', 'sort_order'),
                'sort_direction' => $request->get('sort_direction', 'asc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $services = $this->serviceService->getActiveServices($filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($services, 'Services retrieved successfully');
            }

            return $this->successResponse($services, 'Services retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve services');
        }
    }

    /**
     * Get service by slug
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $service = $this->serviceService->findBySlug($slug);

            if (!$service) {
                return $this->errorResponse('Service not found', 404);
            }

            return $this->successResponse($service, 'Service retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve service');
        }
    }

    /**
     * Get service categories
     */
    public function categories(): JsonResponse
    {
        try {
            $categories = $this->serviceService->getCategories();

            return $this->successResponse($categories, 'Categories retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve categories');
        }
    }
}
