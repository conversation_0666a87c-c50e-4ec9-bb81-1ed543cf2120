<?php

namespace App\Http\Controllers\Api;

use App\Services\ConsultationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class ConsultationController extends BaseApiController
{
    public function __construct(
        private ConsultationService $consultationService
    ) {}

    /**
     * Get user's consultations
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser();
            
            $filters = [
                'status' => $request->get('status'),
                'payment_status' => $request->get('payment_status'),
                'service_id' => $request->get('service_id'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'consultation_date'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => $request->boolean('paginate', true),
                ...$this->getPaginationParams($request),
            ];

            $consultations = $this->consultationService->getUserConsultations($user->id, $filters);

            if ($filters['paginate']) {
                return $this->paginatedResponse($consultations, 'Consultations retrieved successfully');
            }

            return $this->successResponse($consultations, 'Consultations retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve consultations');
        }
    }

    /**
     * Book a consultation
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $consultation = $this->consultationService->bookConsultation($request->all());

            $this->logActivity('consultation_booked', [
                'consultation_id' => $consultation->id,
                'service_id' => $consultation->service_id,
                'consultation_date' => $consultation->consultation_date,
            ]);

            return $this->successResponse(
                $consultation->load(['service', 'user']),
                'Consultation booked successfully',
                201
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), 'not available')) {
                return $this->errorResponse($e->getMessage(), 409);
            }
            return $this->handleException($e, 'Failed to book consultation');
        }
    }

    /**
     * Get consultation details
     */
    public function show(int $id): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser();
            $consultation = $this->consultationService->findByIdOrFail($id);

            // Check if user owns this consultation
            if ($consultation->user_id !== $user->id) {
                return $this->errorResponse('Unauthorized', 403);
            }

            return $this->successResponse(
                $consultation->load(['service', 'user']),
                'Consultation retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve consultation');
        }
    }

    /**
     * Update consultation (limited fields for users)
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $user = $this->getAuthenticatedUser();
            $consultation = $this->consultationService->findByIdOrFail($id);

            // Check if user owns this consultation
            if ($consultation->user_id !== $user->id) {
                return $this->errorResponse('Unauthorized', 403);
            }

            // Only allow updating notes for regular users
            $allowedFields = ['notes'];
            $updateData = $request->only($allowedFields);

            if (empty($updateData)) {
                return $this->errorResponse('No valid fields to update', 400);
            }

            $updatedConsultation = $this->consultationService->update($id, $updateData);

            $this->logActivity('consultation_updated', [
                'consultation_id' => $updatedConsultation->id,
                'updated_fields' => array_keys($updateData),
            ]);

            return $this->successResponse(
                $updatedConsultation->load(['service', 'user']),
                'Consultation updated successfully'
            );
        } catch (ValidationException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to update consultation');
        }
    }

    /**
     * Get available time slots
     */
    public function availableSlots(Request $request): JsonResponse
    {
        try {
            $date = $request->get('date');
            $duration = (int) $request->get('duration', 60);

            if (!$date) {
                return $this->errorResponse('Date parameter is required', 400);
            }

            $slots = $this->consultationService->getAvailableTimeSlots($date, $duration);

            return $this->successResponse($slots, 'Available time slots retrieved successfully');
        } catch (\Exception $e) {
            return $this->handleException($e, 'Failed to retrieve available time slots');
        }
    }
}
