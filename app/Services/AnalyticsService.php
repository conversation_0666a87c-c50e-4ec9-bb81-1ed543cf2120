<?php

namespace App\Services;

use App\Models\AnalyticsEvent;
use App\Models\TrackingSetting;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class AnalyticsService extends BaseService
{
    public function __construct()
    {
        $this->model = new AnalyticsEvent();
    }

    /**
     * Track analytics event
     */
    public function trackEvent(array $data): AnalyticsEvent
    {
        $validated = $this->validateEventData($data);
        
        // Add request metadata
        $validated['session_id'] = session()->getId();
        $validated['page_url'] = request()->url();
        $validated['referrer'] = request()->header('referer');
        $validated['user_agent'] = request()->userAgent();
        $validated['ip_address'] = request()->ip();
        
        // Set user ID if authenticated
        if (auth()->check()) {
            $validated['user_id'] = auth()->id();
        }

        $event = AnalyticsEvent::create($validated);
        
        // Fire server-side tracking
        $this->fireServerSideTracking($event);
        
        return $event;
    }

    /**
     * Get analytics dashboard data
     */
    public function getDashboardData(array $filters = []): array
    {
        $dateFrom = $filters['date_from'] ?? now()->subDays(30)->startOfDay();
        $dateTo = $filters['date_to'] ?? now()->endOfDay();

        return [
            'overview' => $this->getOverviewMetrics($dateFrom, $dateTo),
            'events' => $this->getEventMetrics($dateFrom, $dateTo),
            'conversions' => $this->getConversionMetrics($dateFrom, $dateTo),
            'traffic_sources' => $this->getTrafficSources($dateFrom, $dateTo),
            'popular_pages' => $this->getPopularPages($dateFrom, $dateTo),
        ];
    }

    /**
     * Get analytics events with filters
     */
    public function getEvents(array $filters = [])
    {
        $query = $this->model->with('user');

        $query = $this->applyFilters($query, $filters);

        if (isset($filters['search']) && !empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query = $this->applySorting($query, $sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? true;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Get tracking performance metrics
     */
    public function getTrackingPerformance(array $filters = []): array
    {
        $dateFrom = $filters['date_from'] ?? now()->subDays(7)->startOfDay();
        $dateTo = $filters['date_to'] ?? now()->endOfDay();

        $totalEvents = $this->model
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->count();

        $pixelFired = $this->model
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->where('pixel_fired', true)
            ->count();

        $ga4Fired = $this->model
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->where('ga4_fired', true)
            ->count();

        return [
            'total_events' => $totalEvents,
            'pixel_success_rate' => $totalEvents > 0 ? round(($pixelFired / $totalEvents) * 100, 2) : 0,
            'ga4_success_rate' => $totalEvents > 0 ? round(($ga4Fired / $totalEvents) * 100, 2) : 0,
            'pixel_fired' => $pixelFired,
            'ga4_fired' => $ga4Fired,
        ];
    }

    /**
     * Update tracking settings
     */
    public function updateTrackingSettings(array $settings): bool
    {
        try {
            foreach ($settings as $key => $value) {
                TrackingSetting::updateOrCreate(
                    ['setting_name' => $key],
                    [
                        'setting_value' => $value,
                        'is_active' => true,
                    ]
                );
            }
            
            Log::info('Tracking settings updated', ['settings' => array_keys($settings)]);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update tracking settings: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get tracking settings
     */
    public function getTrackingSettings(): array
    {
        $settings = TrackingSetting::where('is_active', true)->get();
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting->setting_name] = $setting->setting_value;
        }
        
        return $result;
    }

    /**
     * Fire server-side tracking
     */
    private function fireServerSideTracking(AnalyticsEvent $event): void
    {
        try {
            $settings = $this->getTrackingSettings();
            
            // Fire Facebook Conversion API
            if (isset($settings['facebook_pixel_id']) && isset($settings['facebook_access_token'])) {
                $this->fireFacebookConversionAPI($event, $settings);
            }
            
            // Fire GA4 Measurement Protocol
            if (isset($settings['ga4_measurement_id']) && isset($settings['ga4_api_secret'])) {
                $this->fireGA4MeasurementProtocol($event, $settings);
            }
        } catch (\Exception $e) {
            Log::error('Server-side tracking error: ' . $e->getMessage(), [
                'event_id' => $event->id,
                'event_name' => $event->event_name,
            ]);
        }
    }

    /**
     * Fire Facebook Conversion API
     */
    private function fireFacebookConversionAPI(AnalyticsEvent $event, array $settings): void
    {
        // TODO: Implement Facebook Conversion API integration
        // This would send events to Facebook's server-side API
        Log::info('Facebook Conversion API fired', ['event_id' => $event->id]);
    }

    /**
     * Fire GA4 Measurement Protocol
     */
    private function fireGA4MeasurementProtocol(AnalyticsEvent $event, array $settings): void
    {
        // TODO: Implement GA4 Measurement Protocol integration
        // This would send events to Google Analytics 4 server-side API
        Log::info('GA4 Measurement Protocol fired', ['event_id' => $event->id]);
    }

    /**
     * Get overview metrics
     */
    private function getOverviewMetrics($dateFrom, $dateTo): array
    {
        $totalEvents = $this->model->whereBetween('created_at', [$dateFrom, $dateTo])->count();
        $uniqueUsers = $this->model->whereBetween('created_at', [$dateFrom, $dateTo])
            ->whereNotNull('user_id')
            ->distinct('user_id')
            ->count();
        $totalConversions = $this->model->whereBetween('created_at', [$dateFrom, $dateTo])
            ->whereNotNull('conversion_value')
            ->count();
        $conversionValue = $this->model->whereBetween('created_at', [$dateFrom, $dateTo])
            ->sum('conversion_value');

        return [
            'total_events' => $totalEvents,
            'unique_users' => $uniqueUsers,
            'total_conversions' => $totalConversions,
            'conversion_value' => $conversionValue,
        ];
    }

    /**
     * Get event metrics
     */
    private function getEventMetrics($dateFrom, $dateTo): array
    {
        return $this->model
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->selectRaw('event_name, COUNT(*) as count')
            ->groupBy('event_name')
            ->orderByDesc('count')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get conversion metrics
     */
    private function getConversionMetrics($dateFrom, $dateTo): array
    {
        return $this->model
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->whereNotNull('conversion_value')
            ->selectRaw('event_name, COUNT(*) as count, SUM(conversion_value) as total_value')
            ->groupBy('event_name')
            ->orderByDesc('total_value')
            ->get()
            ->toArray();
    }

    /**
     * Get traffic sources
     */
    private function getTrafficSources($dateFrom, $dateTo): array
    {
        return $this->model
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->selectRaw('referrer, COUNT(*) as count')
            ->groupBy('referrer')
            ->orderByDesc('count')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get popular pages
     */
    private function getPopularPages($dateFrom, $dateTo): array
    {
        return $this->model
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->selectRaw('page_url, COUNT(*) as count')
            ->groupBy('page_url')
            ->orderByDesc('count')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['event_name'])) {
            $query->where('event_name', $filters['event_name']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query;
    }

    /**
     * Apply search to query
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('event_name', 'LIKE', "%{$search}%")
              ->orWhere('page_url', 'LIKE', "%{$search}%")
              ->orWhere('referrer', 'LIKE', "%{$search}%");
        });
    }

    /**
     * Get allowed sort fields
     */
    protected function getAllowedSortFields(): array
    {
        return [
            'id', 'event_name', 'conversion_value', 'created_at'
        ];
    }

    /**
     * Validate event data
     */
    private function validateEventData(array $data): array
    {
        return validator($data, [
            'event_name' => 'required|string|max:255',
            'event_parameters' => 'nullable|array',
            'conversion_value' => 'nullable|numeric|min:0',
            'pixel_fired' => 'boolean',
            'ga4_fired' => 'boolean',
        ])->validate();
    }

    /**
     * Get validation rules for create
     */
    protected function getCreateRules(): array
    {
        return [
            'user_id' => 'nullable|exists:users,id',
            'session_id' => 'required|string|max:255',
            'event_name' => 'required|string|max:255',
            'event_parameters' => 'nullable|array',
            'page_url' => 'required|string|max:500',
            'referrer' => 'nullable|string|max:500',
            'user_agent' => 'nullable|string|max:500',
            'ip_address' => 'nullable|ip',
            'pixel_fired' => 'boolean',
            'ga4_fired' => 'boolean',
            'conversion_value' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Get validation rules for update
     */
    protected function getUpdateRules(Model $record): array
    {
        return [
            'pixel_fired' => 'sometimes|boolean',
            'ga4_fired' => 'sometimes|boolean',
            'conversion_value' => 'sometimes|nullable|numeric|min:0',
        ];
    }
}
