<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

abstract class BaseService
{
    /**
     * The model instance
     */
    protected Model $model;

    /**
     * Default pagination limit
     */
    protected int $defaultLimit = 15;

    /**
     * Maximum pagination limit
     */
    protected int $maxLimit = 100;

    /**
     * Get all records with optional pagination and filtering
     */
    public function getAll(array $filters = [], bool $paginate = true): LengthAwarePaginator|array
    {
        $query = $this->model->newQuery();

        // Apply filters
        $query = $this->applyFilters($query, $filters);

        // Apply search
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query = $this->applySorting($query, $sortBy, $sortDirection);

        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get()->toArray();
    }

    /**
     * Find a record by ID
     */
    public function findById(int $id): ?Model
    {
        return $this->model->find($id);
    }

    /**
     * Find a record by ID or fail
     */
    public function findByIdOrFail(int $id): Model
    {
        return $this->model->findOrFail($id);
    }

    /**
     * Create a new record
     */
    public function create(array $data): Model
    {
        try {
            $validated = $this->validateData($data, $this->getCreateRules());
            $record = $this->model->create($validated);
            
            $this->logActivity('created', $record);
            
            return $record;
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Service create error: ' . $e->getMessage(), [
                'service' => get_class($this),
                'data' => $data,
            ]);
            throw $e;
        }
    }

    /**
     * Update a record
     */
    public function update(int $id, array $data): Model
    {
        try {
            $record = $this->findByIdOrFail($id);
            $validated = $this->validateData($data, $this->getUpdateRules($record));
            
            $record->update($validated);
            $record->refresh();
            
            $this->logActivity('updated', $record);
            
            return $record;
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Service update error: ' . $e->getMessage(), [
                'service' => get_class($this),
                'id' => $id,
                'data' => $data,
            ]);
            throw $e;
        }
    }

    /**
     * Delete a record
     */
    public function delete(int $id): bool
    {
        try {
            $record = $this->findByIdOrFail($id);
            $deleted = $record->delete();
            
            if ($deleted) {
                $this->logActivity('deleted', $record);
            }
            
            return $deleted;
        } catch (\Exception $e) {
            Log::error('Service delete error: ' . $e->getMessage(), [
                'service' => get_class($this),
                'id' => $id,
            ]);
            throw $e;
        }
    }

    /**
     * Apply filters to query (to be implemented by child classes)
     */
    protected function applyFilters($query, array $filters)
    {
        return $query;
    }

    /**
     * Apply search to query (to be implemented by child classes)
     */
    protected function applySearch($query, string $search)
    {
        return $query;
    }

    /**
     * Apply sorting to query
     */
    protected function applySorting($query, string $sortBy, string $sortDirection)
    {
        $allowedSortFields = $this->getAllowedSortFields();
        
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }

        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }

        return $query->orderBy($sortBy, $sortDirection);
    }

    /**
     * Get allowed sort fields (to be implemented by child classes)
     */
    protected function getAllowedSortFields(): array
    {
        return ['id', 'created_at', 'updated_at'];
    }

    /**
     * Get validation rules for create (to be implemented by child classes)
     */
    abstract protected function getCreateRules(): array;

    /**
     * Get validation rules for update (to be implemented by child classes)
     */
    abstract protected function getUpdateRules(Model $record): array;

    /**
     * Validate data
     */
    protected function validateData(array $data, array $rules): array
    {
        return validator($data, $rules)->validate();
    }

    /**
     * Log activity
     */
    protected function logActivity(string $action, Model $record): void
    {
        Log::info('Service activity', [
            'service' => get_class($this),
            'action' => $action,
            'model' => get_class($record),
            'record_id' => $record->id,
            'user_id' => auth()->id(),
        ]);
    }

    /**
     * Handle exceptions
     */
    protected function handleException(\Exception $e, string $context = ''): void
    {
        Log::error('Service exception: ' . $e->getMessage(), [
            'service' => get_class($this),
            'context' => $context,
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ]);
    }
}
