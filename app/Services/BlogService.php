<?php

namespace App\Services;

use App\Models\BlogPost;
use App\Models\Category;
use App\Models\Comment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class BlogService extends BaseService
{
    public function __construct()
    {
        $this->model = new BlogPost();
    }

    /**
     * Get published blog posts for public display
     */
    public function getPublishedPosts(array $filters = [])
    {
        $query = $this->model->with(['category', 'author'])
            ->where('status', 'published')
            ->whereNotNull('published_at');

        // Apply category filter
        if (isset($filters['category_id']) && !empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        // Apply search
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'published_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query = $this->applySorting($query, $sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? true;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Find blog post by slug
     */
    public function findBySlug(string $slug): ?BlogPost
    {
        return $this->model->with(['category', 'author', 'comments.user'])
            ->where('slug', $slug)
            ->where('status', 'published')
            ->whereNotNull('published_at')
            ->first();
    }

    /**
     * Increment views count
     */
    public function incrementViews(int $id): void
    {
        $this->model->where('id', $id)->increment('views_count');
    }

    /**
     * Get related posts
     */
    public function getRelatedPosts(BlogPost $post, int $limit = 3): array
    {
        return $this->model->where('id', '!=', $post->id)
            ->where('category_id', $post->category_id)
            ->where('status', 'published')
            ->whereNotNull('published_at')
            ->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Create blog post with auto-generated slug
     */
    public function create(array $data): Model
    {
        // Set author if not provided
        if (empty($data['author_id'])) {
            $data['author_id'] = auth()->id();
        }

        // Generate slug if not provided
        if (empty($data['slug']) && !empty($data['title'])) {
            $data['slug'] = $this->generateUniqueSlug($data['title']);
        }

        // Set published_at if status is published
        if (isset($data['status']) && $data['status'] === 'published' && empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        return parent::create($data);
    }

    /**
     * Update blog post with slug and publish handling
     */
    public function update(int $id, array $data): Model
    {
        $post = $this->findByIdOrFail($id);

        // Generate new slug if title changed and slug not provided
        if (isset($data['title']) && empty($data['slug'])) {
            if ($post->title !== $data['title']) {
                $data['slug'] = $this->generateUniqueSlug($data['title'], $id);
            }
        }

        // Set published_at if status changed to published
        if (isset($data['status']) && $data['status'] === 'published' && $post->status !== 'published') {
            $data['published_at'] = now();
        }

        return parent::update($id, $data);
    }

    /**
     * Generate unique slug
     */
    private function generateUniqueSlug(string $title, int $excludeId = null): string
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slugExists(string $slug, int $excludeId = null): bool
    {
        $query = $this->model->where('slug', $slug);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by status
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Filter by category
        if (isset($filters['category_id']) && !empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        // Filter by author
        if (isset($filters['author_id']) && !empty($filters['author_id'])) {
            $query->where('author_id', $filters['author_id']);
        }

        return $query;
    }

    /**
     * Apply search to query
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'LIKE', "%{$search}%")
              ->orWhere('excerpt', 'LIKE', "%{$search}%")
              ->orWhere('content', 'LIKE', "%{$search}%");
        });
    }

    /**
     * Get allowed sort fields
     */
    protected function getAllowedSortFields(): array
    {
        return [
            'id', 'title', 'status', 'published_at', 'views_count',
            'created_at', 'updated_at'
        ];
    }

    /**
     * Get validation rules for create
     */
    protected function getCreateRules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:blog_posts,slug',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'author_id' => 'nullable|exists:users,id',
            'status' => 'required|in:draft,published',
            'published_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get validation rules for update
     */
    protected function getUpdateRules(Model $record): array
    {
        return [
            'title' => 'sometimes|required|string|max:255',
            'slug' => 'sometimes|nullable|string|max:255|unique:blog_posts,slug,' . $record->id,
            'excerpt' => 'sometimes|nullable|string|max:500',
            'content' => 'sometimes|required|string',
            'featured_image' => 'sometimes|nullable|string|max:255',
            'category_id' => 'sometimes|required|exists:categories,id',
            'author_id' => 'sometimes|nullable|exists:users,id',
            'status' => 'sometimes|required|in:draft,published',
            'published_at' => 'sometimes|nullable|date',
            'meta_title' => 'sometimes|nullable|string|max:255',
            'meta_description' => 'sometimes|nullable|string|max:500',
        ];
    }

    /**
     * Category management methods
     */
    public function getCategories(array $filters = [])
    {
        $query = Category::query();

        if (isset($filters['search']) && !empty($filters['search'])) {
            $query->where('name', 'LIKE', "%{$filters['search']}%");
        }

        return $query->orderBy('name')->get();
    }

    public function createCategory(array $data): Category
    {
        if (empty($data['slug']) && !empty($data['name'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        return Category::create($data);
    }

    public function updateCategory(int $id, array $data): Category
    {
        $category = Category::findOrFail($id);
        
        if (isset($data['name']) && empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        $category->update($data);
        return $category->refresh();
    }

    public function deleteCategory(int $id): bool
    {
        $category = Category::findOrFail($id);
        
        // Check if category has posts
        if ($category->blogPosts()->count() > 0) {
            throw new \Exception('Cannot delete category with existing blog posts');
        }

        return $category->delete();
    }
}
