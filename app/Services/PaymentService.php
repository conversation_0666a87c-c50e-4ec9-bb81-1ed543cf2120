<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Consultation;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class PaymentService extends BaseService
{
    public function __construct()
    {
        $this->model = new Payment();
    }

    /**
     * Create payment record
     */
    public function createPayment(array $data): Payment
    {
        $validated = $this->validatePaymentData($data);
        
        // Set user ID if authenticated
        if (auth()->check()) {
            $validated['user_id'] = auth()->id();
        }

        $payment = Payment::create($validated);
        
        $this->logActivity('payment_created', $payment);
        
        return $payment;
    }

    /**
     * Process Paddle webhook
     */
    public function processPaddleWebhook(array $webhookData): bool
    {
        try {
            // Verify webhook signature (implement based on Paddle documentation)
            if (!$this->verifyPaddleSignature($webhookData)) {
                Log::warning('Invalid Paddle webhook signature');
                return false;
            }

            $eventType = $webhookData['event_type'] ?? null;
            
            switch ($eventType) {
                case 'payment.succeeded':
                    return $this->handlePaddlePaymentSuccess($webhookData);
                case 'payment.failed':
                    return $this->handlePaddlePaymentFailed($webhookData);
                case 'payment.refunded':
                    return $this->handlePaddlePaymentRefunded($webhookData);
                default:
                    Log::info('Unhandled Paddle webhook event', ['event_type' => $eventType]);
                    return true;
            }
        } catch (\Exception $e) {
            Log::error('Paddle webhook processing error: ' . $e->getMessage(), [
                'webhook_data' => $webhookData,
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Process CoinBase webhook
     */
    public function processCoinBaseWebhook(array $webhookData): bool
    {
        try {
            // Verify webhook signature (implement based on CoinBase documentation)
            if (!$this->verifyCoinBaseSignature($webhookData)) {
                Log::warning('Invalid CoinBase webhook signature');
                return false;
            }

            $eventType = $webhookData['event']['type'] ?? null;
            
            switch ($eventType) {
                case 'charge:confirmed':
                    return $this->handleCoinBasePaymentSuccess($webhookData);
                case 'charge:failed':
                    return $this->handleCoinBasePaymentFailed($webhookData);
                case 'charge:refunded':
                    return $this->handleCoinBasePaymentRefunded($webhookData);
                default:
                    Log::info('Unhandled CoinBase webhook event', ['event_type' => $eventType]);
                    return true;
            }
        } catch (\Exception $e) {
            Log::error('CoinBase webhook processing error: ' . $e->getMessage(), [
                'webhook_data' => $webhookData,
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Get user payments
     */
    public function getUserPayments(int $userId, array $filters = [])
    {
        $query = $this->model->with(['consultation.service'])
            ->where('user_id', $userId);

        return $this->applyFiltersAndPaginate($query, $filters);
    }

    /**
     * Get all payments (admin)
     */
    public function getAllPayments(array $filters = [])
    {
        $query = $this->model->with(['user', 'consultation.service']);

        return $this->applyFiltersAndPaginate($query, $filters);
    }

    /**
     * Handle Paddle payment success
     */
    private function handlePaddlePaymentSuccess(array $webhookData): bool
    {
        $paymentGatewayId = $webhookData['data']['id'] ?? null;
        $amount = $webhookData['data']['amount'] ?? null;
        $currency = $webhookData['data']['currency_code'] ?? null;

        if (!$paymentGatewayId) {
            Log::error('Missing payment ID in Paddle webhook');
            return false;
        }

        $payment = $this->model->where('payment_gateway_id', $paymentGatewayId)->first();
        
        if (!$payment) {
            Log::warning('Payment not found for Paddle webhook', ['payment_id' => $paymentGatewayId]);
            return false;
        }

        $payment->update([
            'status' => 'completed',
            'gateway_response' => $webhookData,
        ]);

        // Update consultation payment status
        if ($payment->consultation) {
            app(ConsultationService::class)->updatePaymentStatus(
                $payment->consultation->id,
                'completed',
                $paymentGatewayId
            );
        }

        $this->logActivity('payment_completed', $payment);
        
        return true;
    }

    /**
     * Handle Paddle payment failed
     */
    private function handlePaddlePaymentFailed(array $webhookData): bool
    {
        $paymentGatewayId = $webhookData['data']['id'] ?? null;

        if (!$paymentGatewayId) {
            return false;
        }

        $payment = $this->model->where('payment_gateway_id', $paymentGatewayId)->first();
        
        if ($payment) {
            $payment->update([
                'status' => 'failed',
                'gateway_response' => $webhookData,
            ]);

            if ($payment->consultation) {
                app(ConsultationService::class)->updatePaymentStatus(
                    $payment->consultation->id,
                    'failed'
                );
            }

            $this->logActivity('payment_failed', $payment);
        }

        return true;
    }

    /**
     * Handle Paddle payment refunded
     */
    private function handlePaddlePaymentRefunded(array $webhookData): bool
    {
        $paymentGatewayId = $webhookData['data']['id'] ?? null;

        if (!$paymentGatewayId) {
            return false;
        }

        $payment = $this->model->where('payment_gateway_id', $paymentGatewayId)->first();
        
        if ($payment) {
            $payment->update([
                'status' => 'refunded',
                'gateway_response' => $webhookData,
            ]);

            if ($payment->consultation) {
                app(ConsultationService::class)->updatePaymentStatus(
                    $payment->consultation->id,
                    'refunded'
                );
            }

            $this->logActivity('payment_refunded', $payment);
        }

        return true;
    }

    /**
     * Handle CoinBase payment success
     */
    private function handleCoinBasePaymentSuccess(array $webhookData): bool
    {
        $chargeData = $webhookData['event']['data'] ?? null;
        $paymentGatewayId = $chargeData['id'] ?? null;

        if (!$paymentGatewayId) {
            return false;
        }

        $payment = $this->model->where('payment_gateway_id', $paymentGatewayId)->first();
        
        if ($payment) {
            $payment->update([
                'status' => 'completed',
                'gateway_response' => $webhookData,
            ]);

            if ($payment->consultation) {
                app(ConsultationService::class)->updatePaymentStatus(
                    $payment->consultation->id,
                    'completed',
                    $paymentGatewayId
                );
            }

            $this->logActivity('payment_completed', $payment);
        }

        return true;
    }

    /**
     * Handle CoinBase payment failed
     */
    private function handleCoinBasePaymentFailed(array $webhookData): bool
    {
        $chargeData = $webhookData['event']['data'] ?? null;
        $paymentGatewayId = $chargeData['id'] ?? null;

        if (!$paymentGatewayId) {
            return false;
        }

        $payment = $this->model->where('payment_gateway_id', $paymentGatewayId)->first();
        
        if ($payment) {
            $payment->update([
                'status' => 'failed',
                'gateway_response' => $webhookData,
            ]);

            if ($payment->consultation) {
                app(ConsultationService::class)->updatePaymentStatus(
                    $payment->consultation->id,
                    'failed'
                );
            }

            $this->logActivity('payment_failed', $payment);
        }

        return true;
    }

    /**
     * Handle CoinBase payment refunded
     */
    private function handleCoinBasePaymentRefunded(array $webhookData): bool
    {
        // Similar implementation to failed payment
        return $this->handleCoinBasePaymentFailed($webhookData);
    }

    /**
     * Verify Paddle webhook signature
     */
    private function verifyPaddleSignature(array $webhookData): bool
    {
        // TODO: Implement Paddle signature verification
        // This should verify the webhook signature using Paddle's public key
        return true; // Placeholder
    }

    /**
     * Verify CoinBase webhook signature
     */
    private function verifyCoinBaseSignature(array $webhookData): bool
    {
        // TODO: Implement CoinBase signature verification
        // This should verify the webhook signature using CoinBase's shared secret
        return true; // Placeholder
    }

    /**
     * Apply filters and pagination
     */
    private function applyFiltersAndPaginate($query, array $filters)
    {
        $query = $this->applyFilters($query, $filters);

        if (isset($filters['search']) && !empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query = $this->applySorting($query, $sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? true;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['payment_method'])) {
            $query->where('payment_method', $filters['payment_method']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query;
    }

    /**
     * Apply search to query
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('payment_gateway_id', 'LIKE', "%{$search}%")
              ->orWhereHas('user', function ($userQuery) use ($search) {
                  $userQuery->where('name', 'LIKE', "%{$search}%")
                           ->orWhere('email', 'LIKE', "%{$search}%");
              });
        });
    }

    /**
     * Get allowed sort fields
     */
    protected function getAllowedSortFields(): array
    {
        return [
            'id', 'amount', 'currency', 'status', 'payment_method',
            'created_at', 'updated_at'
        ];
    }

    /**
     * Validate payment data
     */
    private function validatePaymentData(array $data): array
    {
        return validator($data, [
            'user_id' => 'required|exists:users,id',
            'consultation_id' => 'nullable|exists:consultations,id',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'payment_method' => 'required|in:paddle,coinbase',
            'payment_gateway_id' => 'required|string|max:255',
            'status' => 'required|in:pending,completed,failed,refunded',
            'gateway_response' => 'nullable|array',
        ])->validate();
    }

    /**
     * Get validation rules for create
     */
    protected function getCreateRules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'consultation_id' => 'nullable|exists:consultations,id',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'payment_method' => 'required|in:paddle,coinbase',
            'payment_gateway_id' => 'required|string|max:255',
            'status' => 'required|in:pending,completed,failed,refunded',
            'gateway_response' => 'nullable|array',
        ];
    }

    /**
     * Get validation rules for update
     */
    protected function getUpdateRules(Model $record): array
    {
        return [
            'status' => 'sometimes|required|in:pending,completed,failed,refunded',
            'gateway_response' => 'sometimes|nullable|array',
        ];
    }
}
