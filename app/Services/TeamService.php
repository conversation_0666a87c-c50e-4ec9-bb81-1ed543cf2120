<?php

namespace App\Services;

use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class TeamService extends BaseService
{
    public function __construct()
    {
        $this->model = new TeamMember();
    }

    /**
     * Get active team members for public display
     */
    public function getActiveMembers(array $filters = [])
    {
        $query = $this->model->with('user')
            ->where('is_active', true);

        // Apply search
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query = $this->applySorting($query, $sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? false;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Get all team members with user data
     */
    public function getAllWithUsers(array $filters = [])
    {
        $query = $this->model->with('user');

        // Apply filters
        $query = $this->applyFilters($query, $filters);

        // Apply search
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query = $this->applySorting($query, $sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? true;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Create team member with user relationship
     */
    public function create(array $data): Model
    {
        // Validate that user_id exists and user has team_member role
        if (isset($data['user_id'])) {
            $user = User::find($data['user_id']);
            if (!$user || $user->role !== 'team_member') {
                throw new \Exception('User must have team_member role');
            }

            // Check if team member already exists for this user
            if ($this->model->where('user_id', $data['user_id'])->exists()) {
                throw new \Exception('Team member already exists for this user');
            }
        }

        return parent::create($data);
    }

    /**
     * Update team member
     */
    public function update(int $id, array $data): Model
    {
        // Validate user_id if provided
        if (isset($data['user_id'])) {
            $teamMember = $this->findByIdOrFail($id);
            
            // If changing user, validate new user
            if ($teamMember->user_id !== $data['user_id']) {
                $user = User::find($data['user_id']);
                if (!$user || $user->role !== 'team_member') {
                    throw new \Exception('User must have team_member role');
                }

                // Check if team member already exists for this user
                if ($this->model->where('user_id', $data['user_id'])->where('id', '!=', $id)->exists()) {
                    throw new \Exception('Team member already exists for this user');
                }
            }
        }

        return parent::update($id, $data);
    }

    /**
     * Get team member by user ID
     */
    public function findByUserId(int $userId): ?TeamMember
    {
        return $this->model->with('user')->where('user_id', $userId)->first();
    }

    /**
     * Update sort order for multiple team members
     */
    public function updateSortOrder(array $sortData): bool
    {
        try {
            foreach ($sortData as $item) {
                if (isset($item['id']) && isset($item['sort_order'])) {
                    $this->model->where('id', $item['id'])
                        ->update(['sort_order' => $item['sort_order']]);
                }
            }
            return true;
        } catch (\Exception $e) {
            $this->handleException($e, 'updateSortOrder');
            return false;
        }
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by active status
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        // Filter by user ID
        if (isset($filters['user_id']) && !empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        return $query;
    }

    /**
     * Apply search to query
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('position', 'LIKE', "%{$search}%")
              ->orWhere('bio', 'LIKE', "%{$search}%")
              ->orWhereHas('user', function ($userQuery) use ($search) {
                  $userQuery->where('name', 'LIKE', "%{$search}%")
                           ->orWhere('email', 'LIKE', "%{$search}%");
              });
        });
    }

    /**
     * Get allowed sort fields
     */
    protected function getAllowedSortFields(): array
    {
        return [
            'id', 'name', 'position', 'sort_order', 'is_active',
            'created_at', 'updated_at'
        ];
    }

    /**
     * Get validation rules for create
     */
    protected function getCreateRules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'bio' => 'nullable|string',
            'avatar' => 'nullable|string|max:255',
            'expertise' => 'nullable|array',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get validation rules for update
     */
    protected function getUpdateRules(Model $record): array
    {
        return [
            'user_id' => 'sometimes|required|exists:users,id',
            'name' => 'sometimes|required|string|max:255',
            'position' => 'sometimes|required|string|max:255',
            'bio' => 'sometimes|nullable|string',
            'avatar' => 'sometimes|nullable|string|max:255',
            'expertise' => 'sometimes|nullable|array',
            'is_active' => 'sometimes|boolean',
            'sort_order' => 'sometimes|nullable|integer|min:0',
        ];
    }
}
