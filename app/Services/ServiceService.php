<?php

namespace App\Services;

use App\Models\Service;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ServiceService extends BaseService
{
    public function __construct()
    {
        $this->model = new Service();
    }

    /**
     * Get active services for public display
     */
    public function getActiveServices(array $filters = [])
    {
        $query = $this->model->where('is_active', true);

        // Apply category filter
        if (isset($filters['category']) && !empty($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        // Apply search
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query = $this->applySorting($query, $sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? true;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Find service by slug
     */
    public function findBySlug(string $slug): ?Service
    {
        return $this->model->where('slug', $slug)->where('is_active', true)->first();
    }

    /**
     * Get service categories
     */
    public function getCategories(): array
    {
        return $this->model->where('is_active', true)
            ->distinct()
            ->pluck('category')
            ->filter()
            ->values()
            ->toArray();
    }

    /**
     * Create service with auto-generated slug
     */
    public function create(array $data): Model
    {
        // Generate slug if not provided
        if (empty($data['slug']) && !empty($data['title'])) {
            $data['slug'] = $this->generateUniqueSlug($data['title']);
        }

        return parent::create($data);
    }

    /**
     * Update service with slug handling
     */
    public function update(int $id, array $data): Model
    {
        // Generate new slug if title changed and slug not provided
        if (isset($data['title']) && empty($data['slug'])) {
            $service = $this->findByIdOrFail($id);
            if ($service->title !== $data['title']) {
                $data['slug'] = $this->generateUniqueSlug($data['title'], $id);
            }
        }

        return parent::update($id, $data);
    }

    /**
     * Generate unique slug
     */
    private function generateUniqueSlug(string $title, int $excludeId = null): string
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slugExists(string $slug, int $excludeId = null): bool
    {
        $query = $this->model->where('slug', $slug);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by active status
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        // Filter by category
        if (isset($filters['category']) && !empty($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        return $query;
    }

    /**
     * Apply search to query
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('detailed_description', 'LIKE', "%{$search}%")
              ->orWhere('category', 'LIKE', "%{$search}%");
        });
    }

    /**
     * Get allowed sort fields
     */
    protected function getAllowedSortFields(): array
    {
        return [
            'id', 'title', 'category', 'price_range', 'sort_order', 
            'is_active', 'created_at', 'updated_at'
        ];
    }

    /**
     * Get validation rules for create
     */
    protected function getCreateRules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:services,slug',
            'description' => 'required|string|max:500',
            'detailed_description' => 'sometimes|nullable|string',
            'features' => 'sometimes|nullable|array',
            'price_range' => 'sometimes|nullable|string|max:100',
            'category' => 'required|string|max:100',
            'is_active' => 'sometimes|boolean',
            'sort_order' => 'sometimes|nullable|integer|min:0',
            'meta_title' => 'sometimes|nullable|string|max:255',
            'meta_description' => 'sometimes|nullable|string|max:500',
        ];
    }

    /**
     * Get validation rules for update
     */
    protected function getUpdateRules(Model $record): array
    {
        return [
            'title' => 'sometimes|required|string|max:255',
            'slug' => 'sometimes|nullable|string|max:255|unique:services,slug,' . $record->id,
            'description' => 'sometimes|required|string|max:500',
            'detailed_description' => 'sometimes|nullable|string',
            'features' => 'sometimes|nullable|array',
            'price_range' => 'sometimes|nullable|string|max:100',
            'category' => 'sometimes|required|string|max:100',
            'is_active' => 'sometimes|boolean',
            'sort_order' => 'sometimes|nullable|integer|min:0',
            'meta_title' => 'sometimes|nullable|string|max:255',
            'meta_description' => 'sometimes|nullable|string|max:500',
        ];
    }
}
