<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AnalyticsEvent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'session_id',
        'event_name',
        'event_parameters',
        'page_url',
        'referrer',
        'user_agent',
        'ip_address',
        'pixel_fired',
        'ga4_fired',
        'conversion_value',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'event_parameters' => 'array',
        'pixel_fired' => 'boolean',
        'ga4_fired' => 'boolean',
        'conversion_value' => 'decimal:2',
    ];

    /**
     * Scope a query to filter by event name.
     */
    public function scopeByEvent($query, $eventName)
    {
        return $query->where('event_name', $eventName);
    }

    /**
     * Scope a query to filter by session.
     */
    public function scopeBySession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include conversion events.
     */
    public function scopeConversions($query)
    {
        return $query->whereNotNull('conversion_value');
    }

    /**
     * Scope a query to only include events where pixel fired.
     */
    public function scopePixelFired($query)
    {
        return $query->where('pixel_fired', true);
    }

    /**
     * Scope a query to only include events where GA4 fired.
     */
    public function scopeGa4Fired($query)
    {
        return $query->where('ga4_fired', true);
    }

    /**
     * Get the user that owns the analytics event.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if both tracking pixels fired successfully.
     */
    public function bothPixelsFired()
    {
        return $this->pixel_fired && $this->ga4_fired;
    }

    /**
     * Get the event parameter by key.
     */
    public function getParameter($key, $default = null)
    {
        return $this->event_parameters[$key] ?? $default;
    }

    /**
     * Set an event parameter.
     */
    public function setParameter($key, $value)
    {
        $parameters = $this->event_parameters ?? [];
        $parameters[$key] = $value;
        $this->event_parameters = $parameters;
        return $this;
    }
}
