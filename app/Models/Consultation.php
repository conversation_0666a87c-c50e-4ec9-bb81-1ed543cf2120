<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Consultation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'service_id',
        'consultation_date',
        'duration',
        'status',
        'notes',
        'meeting_link',
        'payment_status',
        'payment_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'consultation_date' => 'datetime',
        'duration' => 'integer',
    ];

    /**
     * Scope a query to only include pending consultations.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include confirmed consultations.
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope a query to only include completed consultations.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include upcoming consultations.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('consultation_date', '>', now())
                    ->whereIn('status', ['pending', 'confirmed']);
    }

    /**
     * Scope a query to only include past consultations.
     */
    public function scopePast($query)
    {
        return $query->where('consultation_date', '<', now());
    }

    /**
     * Get the user that owns the consultation.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the service for the consultation.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the payment for the consultation.
     */
    public function payment()
    {
        return $this->hasOne(Payment::class);
    }

    /**
     * Check if consultation is upcoming.
     */
    public function isUpcoming()
    {
        return $this->consultation_date > now() && in_array($this->status, ['pending', 'confirmed']);
    }

    /**
     * Check if consultation is past.
     */
    public function isPast()
    {
        return $this->consultation_date < now();
    }

    /**
     * Check if consultation can be cancelled.
     */
    public function canBeCancelled()
    {
        return in_array($this->status, ['pending', 'confirmed']) && $this->consultation_date > now()->addHours(24);
    }

    /**
     * Get the formatted duration.
     */
    public function getFormattedDurationAttribute()
    {
        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;

        if ($hours > 0) {
            return $hours . 'h' . ($minutes > 0 ? ' ' . $minutes . 'm' : '');
        }

        return $minutes . 'm';
    }
}
