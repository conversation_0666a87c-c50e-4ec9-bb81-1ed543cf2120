<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrackingSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'setting_name',
        'setting_value',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'setting_value' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active settings.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get a setting value by name.
     */
    public static function getValue($name, $default = null)
    {
        $setting = static::where('setting_name', $name)->where('is_active', true)->first();
        return $setting ? $setting->setting_value : $default;
    }

    /**
     * Set a setting value by name.
     */
    public static function setValue($name, $value, $isActive = true)
    {
        return static::updateOrCreate(
            ['setting_name' => $name],
            [
                'setting_value' => $value,
                'is_active' => $isActive,
            ]
        );
    }

    /**
     * Get Facebook Pixel settings.
     */
    public static function getFacebookPixelSettings()
    {
        return static::getValue('facebook_pixel', [
            'pixel_id' => null,
            'access_token' => null,
            'test_event_code' => null,
            'enabled' => false,
        ]);
    }

    /**
     * Get Google Analytics 4 settings.
     */
    public static function getGA4Settings()
    {
        return static::getValue('google_analytics_4', [
            'measurement_id' => null,
            'api_secret' => null,
            'enabled' => false,
        ]);
    }

    /**
     * Get Google Tag Manager settings.
     */
    public static function getGTMSettings()
    {
        return static::getValue('google_tag_manager', [
            'container_id' => null,
            'enabled' => false,
        ]);
    }

    /**
     * Get privacy settings.
     */
    public static function getPrivacySettings()
    {
        return static::getValue('privacy_settings', [
            'cookie_consent_required' => true,
            'data_retention_days' => 365,
            'anonymize_ip' => true,
        ]);
    }

    /**
     * Check if a specific tracking service is enabled.
     */
    public static function isServiceEnabled($service)
    {
        $settings = static::getValue($service, ['enabled' => false]);
        return $settings['enabled'] ?? false;
    }
}
