import { describe, it, expect } from 'vitest';
import {
  formatDate,
  formatRelativeTime,
  formatCurrency,
  formatNumber,
  formatPhoneNumber,
  truncateText,
  generateSlug,
  calculateReadingTime,
  formatFileSize,
  isValidEmail,
  isValidPhoneNumber,
  getInitials,
  toTitleCase,
} from '../formatters';

describe('formatDate', () => {
  it('formats date with default options', () => {
    const result = formatDate('2025-01-15T10:30:00Z');
    expect(result).toBe('Jan 15, 2025');
  });

  it('formats date with custom options', () => {
    const result = formatDate('2025-01-15T10:30:00Z', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    expect(result).toBe('January 15, 2025');
  });
});

describe('formatRelativeTime', () => {
  it('returns "just now" for very recent dates', () => {
    const now = new Date();
    const result = formatRelativeTime(now.toISOString());
    expect(result).toBe('just now');
  });

  it('formats minutes ago', () => {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const result = formatRelativeTime(fiveMinutesAgo.toISOString());
    expect(result).toBe('5 minutes ago');
  });

  it('formats hours ago', () => {
    const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
    const result = formatRelativeTime(twoHoursAgo.toISOString());
    expect(result).toBe('2 hours ago');
  });

  it('formats days ago', () => {
    const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
    const result = formatRelativeTime(threeDaysAgo.toISOString());
    expect(result).toBe('3 days ago');
  });
});

describe('formatCurrency', () => {
  it('formats USD currency by default', () => {
    const result = formatCurrency(1234.56);
    expect(result).toBe('$1,234.56');
  });

  it('formats different currencies', () => {
    const result = formatCurrency(1234.56, 'EUR');
    expect(result).toBe('€1,234.56');
  });

  it('handles zero amount', () => {
    const result = formatCurrency(0);
    expect(result).toBe('$0.00');
  });
});

describe('formatNumber', () => {
  it('formats numbers with commas', () => {
    expect(formatNumber(1234)).toBe('1,234');
    expect(formatNumber(1234567)).toBe('1,234,567');
  });

  it('handles small numbers', () => {
    expect(formatNumber(123)).toBe('123');
  });

  it('handles zero', () => {
    expect(formatNumber(0)).toBe('0');
  });
});

describe('formatPhoneNumber', () => {
  it('formats 10-digit US phone number', () => {
    const result = formatPhoneNumber('1234567890');
    expect(result).toBe('(*************');
  });

  it('formats 11-digit phone number with country code', () => {
    const result = formatPhoneNumber('11234567890');
    expect(result).toBe('+1 (*************');
  });

  it('returns original for invalid format', () => {
    const result = formatPhoneNumber('123');
    expect(result).toBe('123');
  });

  it('handles phone number with existing formatting', () => {
    const result = formatPhoneNumber('(*************');
    expect(result).toBe('(*************');
  });
});

describe('truncateText', () => {
  it('truncates text longer than max length', () => {
    const result = truncateText('This is a long text that should be truncated', 20);
    expect(result).toBe('This is a long text...');
  });

  it('returns original text if shorter than max length', () => {
    const result = truncateText('Short text', 20);
    expect(result).toBe('Short text');
  });

  it('handles exact length', () => {
    const result = truncateText('Exactly twenty chars', 20);
    expect(result).toBe('Exactly twenty chars');
  });
});

describe('generateSlug', () => {
  it('converts text to lowercase slug', () => {
    const result = generateSlug('Hello World');
    expect(result).toBe('hello-world');
  });

  it('removes special characters', () => {
    const result = generateSlug('Hello, World! & More');
    expect(result).toBe('hello-world-more');
  });

  it('handles multiple spaces', () => {
    const result = generateSlug('Hello    World');
    expect(result).toBe('hello-world');
  });

  it('removes leading and trailing hyphens', () => {
    const result = generateSlug('  Hello World  ');
    expect(result).toBe('hello-world');
  });
});

describe('calculateReadingTime', () => {
  it('calculates reading time for short text', () => {
    const text = 'This is a short text with about ten words here.';
    const result = calculateReadingTime(text);
    expect(result).toBe(1);
  });

  it('calculates reading time for longer text', () => {
    const words = Array(400).fill('word').join(' ');
    const result = calculateReadingTime(words);
    expect(result).toBe(2);
  });

  it('uses custom words per minute', () => {
    const words = Array(300).fill('word').join(' ');
    const result = calculateReadingTime(words, 100);
    expect(result).toBe(3);
  });
});

describe('formatFileSize', () => {
  it('formats bytes', () => {
    expect(formatFileSize(0)).toBe('0 Bytes');
    expect(formatFileSize(500)).toBe('500 Bytes');
  });

  it('formats kilobytes', () => {
    expect(formatFileSize(1024)).toBe('1 KB');
    expect(formatFileSize(1536)).toBe('1.5 KB');
  });

  it('formats megabytes', () => {
    expect(formatFileSize(1048576)).toBe('1 MB');
    expect(formatFileSize(2097152)).toBe('2 MB');
  });

  it('formats gigabytes', () => {
    expect(formatFileSize(1073741824)).toBe('1 GB');
  });
});

describe('isValidEmail', () => {
  it('validates correct email addresses', () => {
    expect(isValidEmail('<EMAIL>')).toBe(true);
    expect(isValidEmail('<EMAIL>')).toBe(true);
    expect(isValidEmail('<EMAIL>')).toBe(true);
  });

  it('rejects invalid email addresses', () => {
    expect(isValidEmail('invalid-email')).toBe(false);
    expect(isValidEmail('test@')).toBe(false);
    expect(isValidEmail('@example.com')).toBe(false);
    expect(isValidEmail('<EMAIL>')).toBe(false);
  });
});

describe('isValidPhoneNumber', () => {
  it('validates correct phone numbers', () => {
    expect(isValidPhoneNumber('1234567890')).toBe(true);
    expect(isValidPhoneNumber('+11234567890')).toBe(true);
    expect(isValidPhoneNumber('(*************')).toBe(true);
  });

  it('rejects invalid phone numbers', () => {
    expect(isValidPhoneNumber('123')).toBe(false);
    expect(isValidPhoneNumber('abc1234567')).toBe(false);
    expect(isValidPhoneNumber('')).toBe(false);
  });
});

describe('getInitials', () => {
  it('gets initials from full name', () => {
    expect(getInitials('John Doe')).toBe('JD');
    expect(getInitials('Jane Mary Smith')).toBe('JM');
  });

  it('handles single name', () => {
    expect(getInitials('John')).toBe('J');
  });

  it('handles empty string', () => {
    expect(getInitials('')).toBe('');
  });

  it('limits to 2 characters', () => {
    expect(getInitials('John Mary Jane Doe')).toBe('JM');
  });
});

describe('toTitleCase', () => {
  it('converts to title case', () => {
    expect(toTitleCase('hello world')).toBe('Hello World');
    expect(toTitleCase('HELLO WORLD')).toBe('Hello World');
    expect(toTitleCase('hELLo WoRLd')).toBe('Hello World');
  });

  it('handles single word', () => {
    expect(toTitleCase('hello')).toBe('Hello');
  });

  it('handles empty string', () => {
    expect(toTitleCase('')).toBe('');
  });
});
