import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import PublicLayout from '../public-layout';

// Mock the header and footer components
vi.mock('@/components/public-header', () => ({
  PublicHeader: () => <header data-testid="public-header">Public Header</header>,
}));

vi.mock('@/components/public-footer', () => ({
  PublicFooter: () => <footer data-testid="public-footer">Public Footer</footer>,
}));

describe('PublicLayout Component', () => {
  it('renders header, main content, and footer', () => {
    render(
      <PublicLayout>
        <div>Test content</div>
      </PublicLayout>
    );
    
    expect(screen.getByTestId('public-header')).toBeInTheDocument();
    expect(screen.getByText('Test content')).toBeInTheDocument();
    expect(screen.getByTestId('public-footer')).toBeInTheDocument();
  });

  it('applies custom className to main element', () => {
    const { container } = render(
      <PublicLayout className="custom-main-class">
        <div>Test content</div>
      </PublicLayout>
    );
    
    const mainElement = container.querySelector('main');
    expect(mainElement).toHaveClass('custom-main-class');
  });

  it('has correct layout structure', () => {
    const { container } = render(
      <PublicLayout>
        <div>Test content</div>
      </PublicLayout>
    );
    
    const layoutContainer = container.firstChild;
    expect(layoutContainer).toHaveClass('flex', 'min-h-screen', 'flex-col');
    
    const mainElement = container.querySelector('main');
    expect(mainElement).toHaveClass('flex-1');
  });

  it('renders children correctly', () => {
    render(
      <PublicLayout>
        <div>Child 1</div>
        <div>Child 2</div>
      </PublicLayout>
    );
    
    expect(screen.getByText('Child 1')).toBeInTheDocument();
    expect(screen.getByText('Child 2')).toBeInTheDocument();
  });

  it('accepts title and description props (for potential future use)', () => {
    // These props are defined in the interface but not currently used
    // This test ensures the component accepts them without errors
    render(
      <PublicLayout title="Test Title" description="Test Description">
        <div>Test content</div>
      </PublicLayout>
    );
    
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });
});
