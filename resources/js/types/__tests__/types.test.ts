import { describe, it, expect } from 'vitest';
import type { User, Service, TeamMember, Consultation, Payment } from '../index';

describe('TypeScript Interfaces', () => {
  it('User interface has correct structure', () => {
    const user: User = {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'client',
      avatar: 'avatar.jpg',
      phone: '+1234567890',
      company: 'Test Company',
      email_verified_at: '2025-01-01T00:00:00.000Z',
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-01T00:00:00.000Z',
    };

    expect(user.id).toBe(1);
    expect(user.name).toBe('<PERSON>');
    expect(user.role).toBe('client');
    expect(['admin', 'client', 'team_member']).toContain(user.role);
  });

  it('Service interface has correct structure', () => {
    const service: Service = {
      id: 1,
      title: 'Test Service',
      slug: 'test-service',
      description: 'Test description',
      detailed_description: 'Detailed description',
      features: ['Feature 1', 'Feature 2'],
      price_range: '$100-$200',
      category: 'Meta Ads',
      is_active: true,
      sort_order: 1,
      meta_title: 'Test Service',
      meta_description: 'Test meta description',
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-01T00:00:00.000Z',
    };

    expect(service.id).toBe(1);
    expect(service.title).toBe('Test Service');
    expect(service.is_active).toBe(true);
    expect(Array.isArray(service.features)).toBe(true);
  });

  it('TeamMember interface has correct structure', () => {
    const teamMember: TeamMember = {
      id: 1,
      user_id: 1,
      name: 'Team Member',
      position: 'Developer',
      bio: 'Bio text',
      avatar: 'avatar.jpg',
      expertise: ['PHP', 'Laravel'],
      is_active: true,
      sort_order: 1,
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-01T00:00:00.000Z',
    };

    expect(teamMember.id).toBe(1);
    expect(teamMember.user_id).toBe(1);
    expect(teamMember.is_active).toBe(true);
    expect(Array.isArray(teamMember.expertise)).toBe(true);
  });

  it('Consultation interface has correct structure', () => {
    const consultation: Consultation = {
      id: 1,
      user_id: 1,
      service_id: 1,
      consultation_date: '2025-01-01T10:00:00.000Z',
      duration: 60,
      status: 'pending',
      notes: 'Test notes',
      meeting_link: 'https://meet.example.com',
      payment_status: 'pending',
      payment_id: 'pay_123',
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-01T00:00:00.000Z',
    };

    expect(consultation.id).toBe(1);
    expect(consultation.duration).toBe(60);
    expect(['pending', 'confirmed', 'completed', 'cancelled']).toContain(consultation.status);
    expect(['pending', 'paid', 'refunded']).toContain(consultation.payment_status);
  });

  it('Payment interface has correct structure', () => {
    const payment: Payment = {
      id: 1,
      user_id: 1,
      consultation_id: 1,
      amount: 100.00,
      currency: 'USD',
      payment_method: 'paddle',
      payment_gateway_id: 'gw_123',
      status: 'completed',
      gateway_response: { success: true },
      created_at: '2025-01-01T00:00:00.000Z',
      updated_at: '2025-01-01T00:00:00.000Z',
    };

    expect(payment.id).toBe(1);
    expect(payment.amount).toBe(100.00);
    expect(['paddle', 'coinbase']).toContain(payment.payment_method);
    expect(['pending', 'completed', 'failed', 'refunded']).toContain(payment.status);
  });
});
