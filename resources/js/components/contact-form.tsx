import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useForm } from '@inertiajs/react';
import { LoaderCircle, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { useState } from 'react';

interface ContactFormProps {
    title?: string;
    description?: string;
    className?: string;
    showServiceSelection?: boolean;
}

const serviceOptions = [
    { value: 'meta-ads', label: 'Meta Ads Management' },
    { value: 'facebook-pixel', label: 'Facebook Pixel Setup' },
    { value: 'google-analytics', label: 'Google Analytics Setup' },
    { value: 'conversion-tracking', label: 'Conversion Tracking' },
    { value: 'consultation', label: 'General Consultation' },
    { value: 'other', label: 'Other' },
];

const budgetOptions = [
    { value: 'under-1k', label: 'Under $1,000' },
    { value: '1k-5k', label: '$1,000 - $5,000' },
    { value: '5k-10k', label: '$5,000 - $10,000' },
    { value: '10k-25k', label: '$10,000 - $25,000' },
    { value: 'over-25k', label: 'Over $25,000' },
];

export function ContactForm({ 
    title = "Get In Touch",
    description = "Ready to boost your digital marketing? Let's discuss your project and how we can help you achieve your goals.",
    className = '',
    showServiceSelection = true
}: ContactFormProps) {
    const [isSubmitted, setIsSubmitted] = useState(false);
    
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        company: '',
        phone: '',
        service_interest: '',
        budget_range: '',
        message: '',
        newsletter_consent: false,
        privacy_consent: false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        post('/api/contact', {
            onSuccess: () => {
                setIsSubmitted(true);
                reset();
            },
        });
    };

    if (isSubmitted) {
        return (
            <Card className={className}>
                <CardContent className="p-8 text-center">
                    <div className="flex justify-center mb-4">
                        <CheckCircle className="h-16 w-16 text-green-500" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">Thank You!</h3>
                    <p className="text-muted-foreground mb-4">
                        Your message has been sent successfully. We'll get back to you within 24 hours.
                    </p>
                    <Button onClick={() => setIsSubmitted(false)} variant="outline">
                        Send Another Message
                    </Button>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle>{title}</CardTitle>
                <CardDescription>{description}</CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Personal Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="name">Full Name *</Label>
                            <Input
                                id="name"
                                type="text"
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                                placeholder="John Doe"
                                required
                            />
                            {errors.name && (
                                <p className="text-sm text-red-500">{errors.name}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="email">Email Address *</Label>
                            <Input
                                id="email"
                                type="email"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                                placeholder="<EMAIL>"
                                required
                            />
                            {errors.email && (
                                <p className="text-sm text-red-500">{errors.email}</p>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="company">Company</Label>
                            <Input
                                id="company"
                                type="text"
                                value={data.company}
                                onChange={(e) => setData('company', e.target.value)}
                                placeholder="Your Company"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="phone">Phone Number</Label>
                            <Input
                                id="phone"
                                type="tel"
                                value={data.phone}
                                onChange={(e) => setData('phone', e.target.value)}
                                placeholder="+****************"
                            />
                        </div>
                    </div>

                    {/* Service Selection */}
                    {showServiceSelection && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="service">Service Interest</Label>
                                <Select value={data.service_interest} onValueChange={(value) => setData('service_interest', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a service" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {serviceOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="budget">Budget Range</Label>
                                <Select value={data.budget_range} onValueChange={(value) => setData('budget_range', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select budget range" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {budgetOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    )}

                    {/* Message */}
                    <div className="space-y-2">
                        <Label htmlFor="message">Message *</Label>
                        <Textarea
                            id="message"
                            value={data.message}
                            onChange={(e) => setData('message', e.target.value)}
                            placeholder="Tell us about your project, goals, and how we can help you..."
                            rows={5}
                            required
                        />
                        {errors.message && (
                            <p className="text-sm text-red-500">{errors.message}</p>
                        )}
                    </div>

                    {/* Consent Checkboxes */}
                    <div className="space-y-3">
                        <div className="flex items-start space-x-2">
                            <Checkbox
                                id="newsletter"
                                checked={data.newsletter_consent}
                                onCheckedChange={(checked) => setData('newsletter_consent', checked as boolean)}
                            />
                            <Label htmlFor="newsletter" className="text-sm leading-relaxed">
                                I'd like to receive updates about ConvertOKit services and digital marketing insights.
                            </Label>
                        </div>

                        <div className="flex items-start space-x-2">
                            <Checkbox
                                id="privacy"
                                checked={data.privacy_consent}
                                onCheckedChange={(checked) => setData('privacy_consent', checked as boolean)}
                                required
                            />
                            <Label htmlFor="privacy" className="text-sm leading-relaxed">
                                I agree to the <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a> and 
                                <a href="/terms" className="text-primary hover:underline ml-1">Terms of Service</a>. *
                            </Label>
                        </div>
                    </div>

                    {/* Error Alert */}
                    {Object.keys(errors).length > 0 && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                                Please fix the errors above and try again.
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Submit Button */}
                    <Button 
                        type="submit" 
                        className="w-full" 
                        disabled={processing || !data.privacy_consent}
                    >
                        {processing ? (
                            <>
                                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                                Sending...
                            </>
                        ) : (
                            <>
                                <Send className="mr-2 h-4 w-4" />
                                Send Message
                            </>
                        )}
                    </Button>
                </form>
            </CardContent>
        </Card>
    );
}
