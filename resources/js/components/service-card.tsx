import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { type Service } from '@/types';
import { Link } from '@inertiajs/react';
import { ArrowRight, Check, Star } from 'lucide-react';

interface ServiceCardProps {
    service: Service;
    featured?: boolean;
    className?: string;
}

export function ServiceCard({ service, featured = false, className = '' }: ServiceCardProps) {
    const features = Array.isArray(service.features) ? service.features : [];
    
    return (
        <Card className={`relative transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${featured ? 'border-primary shadow-md' : ''} ${className}`}>
            {featured && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground px-3 py-1">
                        <Star className="w-3 h-3 mr-1" />
                        Most Popular
                    </Badge>
                </div>
            )}
            
            <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <CardTitle className="text-xl mb-2 line-clamp-2">
                            {service.title}
                        </CardTitle>
                        <CardDescription className="text-sm text-muted-foreground line-clamp-3">
                            {service.description}
                        </CardDescription>
                    </div>
                </div>
                
                {service.category && (
                    <Badge variant="secondary" className="w-fit mt-2">
                        {service.category}
                    </Badge>
                )}
            </CardHeader>

            <CardContent className="pb-4">
                {/* Price Range */}
                {service.price_range && (
                    <div className="mb-4">
                        <span className="text-2xl font-bold text-foreground">
                            {service.price_range}
                        </span>
                        <span className="text-sm text-muted-foreground ml-1">
                            / project
                        </span>
                    </div>
                )}

                {/* Features List */}
                {features.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-medium text-sm text-foreground mb-3">What's included:</h4>
                        <ul className="space-y-2">
                            {features.slice(0, 4).map((feature, index) => (
                                <li key={index} className="flex items-start space-x-2 text-sm">
                                    <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-muted-foreground">{feature}</span>
                                </li>
                            ))}
                            {features.length > 4 && (
                                <li className="text-sm text-muted-foreground ml-6">
                                    +{features.length - 4} more features
                                </li>
                            )}
                        </ul>
                    </div>
                )}
            </CardContent>

            <CardFooter className="pt-4 border-t">
                <div className="flex flex-col sm:flex-row gap-2 w-full">
                    <Button className="flex-1" asChild>
                        <Link href={`/services/${service.slug}`}>
                            Learn More
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                    </Button>
                    <Button variant="outline" asChild>
                        <Link href="/book-consultation">
                            Book Now
                        </Link>
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
}

interface ServiceGridProps {
    services: Service[];
    featuredServiceId?: number;
    className?: string;
}

export function ServiceGrid({ services, featuredServiceId, className = '' }: ServiceGridProps) {
    return (
        <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
            {services.map((service) => (
                <ServiceCard
                    key={service.id}
                    service={service}
                    featured={service.id === featuredServiceId}
                />
            ))}
        </div>
    );
}
