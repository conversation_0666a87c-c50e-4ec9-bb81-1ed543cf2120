import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Link } from '@inertiajs/react';
import { Facebook, Twitter, Linkedin, Instagram, Mail, Phone, MapPin } from 'lucide-react';
import AppLogoIcon from './app-logo-icon';

const footerLinks = {
    services: [
        { title: 'Meta Ads Management', href: '/services/meta-ads' },
        { title: 'Facebook Pixel Setup', href: '/services/facebook-pixel' },
        { title: 'Google Analytics', href: '/services/google-analytics' },
        { title: 'Conversion Tracking', href: '/services/conversion-tracking' },
    ],
    company: [
        { title: 'About Us', href: '/about' },
        { title: 'Our Team', href: '/team' },
        { title: 'Blog', href: '/blog' },
        { title: 'Contact', href: '/contact' },
    ],
    resources: [
        { title: 'Case Studies', href: '/blog?category=case-studies' },
        { title: 'Free Resources', href: '/resources' },
        { title: 'Privacy Policy', href: '/privacy' },
        { title: 'Terms of Service', href: '/terms' },
    ],
};

const socialLinks = [
    { icon: Facebook, href: 'https://facebook.com/convertokit', label: 'Facebook' },
    { icon: Twitter, href: 'https://twitter.com/convertokit', label: 'Twitter' },
    { icon: Linkedin, href: 'https://linkedin.com/company/convertokit', label: 'LinkedIn' },
    { icon: Instagram, href: 'https://instagram.com/convertokit', label: 'Instagram' },
];

export function PublicFooter() {
    return (
        <footer className="bg-muted/50 border-t">
            <div className="container mx-auto px-4 py-12">
                {/* Main Footer Content */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
                    {/* Company Info */}
                    <div className="lg:col-span-2">
                        <div className="flex items-center space-x-2 mb-4">
                            <AppLogoIcon className="h-8 w-8" />
                            <span className="text-xl font-bold">ConvertOKit</span>
                        </div>
                        <p className="text-muted-foreground mb-4 max-w-md">
                            Expert Meta Ads & Web Analytics services to help businesses maximize their digital marketing ROI 
                            and track conversions effectively.
                        </p>
                        <div className="space-y-2 text-sm text-muted-foreground">
                            <div className="flex items-center space-x-2">
                                <Mail className="h-4 w-4" />
                                <a href="mailto:<EMAIL>" className="hover:text-foreground">
                                    <EMAIL>
                                </a>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Phone className="h-4 w-4" />
                                <a href="tel:+1234567890" className="hover:text-foreground">
                                    +1 (234) 567-890
                                </a>
                            </div>
                            <div className="flex items-center space-x-2">
                                <MapPin className="h-4 w-4" />
                                <span>Remote & Global Services</span>
                            </div>
                        </div>
                    </div>

                    {/* Services */}
                    <div>
                        <h3 className="font-semibold mb-4">Services</h3>
                        <ul className="space-y-2">
                            {footerLinks.services.map((link) => (
                                <li key={link.href}>
                                    <Link 
                                        href={link.href} 
                                        className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                                    >
                                        {link.title}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Company */}
                    <div>
                        <h3 className="font-semibold mb-4">Company</h3>
                        <ul className="space-y-2">
                            {footerLinks.company.map((link) => (
                                <li key={link.href}>
                                    <Link 
                                        href={link.href} 
                                        className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                                    >
                                        {link.title}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Resources */}
                    <div>
                        <h3 className="font-semibold mb-4">Resources</h3>
                        <ul className="space-y-2">
                            {footerLinks.resources.map((link) => (
                                <li key={link.href}>
                                    <Link 
                                        href={link.href} 
                                        className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                                    >
                                        {link.title}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>

                {/* Newsletter Signup */}
                <div className="mt-12 pt-8 border-t">
                    <div className="max-w-md">
                        <h3 className="font-semibold mb-2">Stay Updated</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                            Get the latest insights on Meta Ads and analytics delivered to your inbox.
                        </p>
                        <div className="flex space-x-2">
                            <Input 
                                type="email" 
                                placeholder="Enter your email" 
                                className="flex-1"
                            />
                            <Button type="submit">Subscribe</Button>
                        </div>
                    </div>
                </div>

                <Separator className="my-8" />

                {/* Bottom Footer */}
                <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div className="text-sm text-muted-foreground">
                        © {new Date().getFullYear()} ConvertOKit. All rights reserved.
                    </div>
                    
                    {/* Social Links */}
                    <div className="flex items-center space-x-4">
                        {socialLinks.map((social) => (
                            <a
                                key={social.label}
                                href={social.href}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-muted-foreground hover:text-foreground transition-colors"
                                aria-label={social.label}
                            >
                                <social.icon className="h-5 w-5" />
                            </a>
                        ))}
                    </div>
                </div>
            </div>
        </footer>
    );
}
