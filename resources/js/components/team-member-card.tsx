import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { type TeamMember } from '@/types';
import { Link } from '@inertiajs/react';
import { Mail, Linkedin, ExternalLink } from 'lucide-react';

interface TeamMemberCardProps {
    member: TeamMember;
    className?: string;
    showContact?: boolean;
}

export function TeamMemberCard({ member, className = '', showContact = true }: TeamMemberCardProps) {
    const expertise = Array.isArray(member.expertise) ? member.expertise : [];
    
    // Generate initials from name
    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return (
        <Card className={`transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${className}`}>
            <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                    <Avatar className="h-24 w-24 border-4 border-background shadow-lg">
                        <AvatarImage 
                            src={member.avatar} 
                            alt={member.name}
                            className="object-cover"
                        />
                        <AvatarFallback className="text-lg font-semibold bg-primary/10 text-primary">
                            {getInitials(member.name)}
                        </AvatarFallback>
                    </Avatar>
                </div>
                
                <CardTitle className="text-xl mb-1">
                    {member.name}
                </CardTitle>
                
                <CardDescription className="text-primary font-medium">
                    {member.position}
                </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
                {/* Bio */}
                {member.bio && (
                    <p className="text-sm text-muted-foreground text-center leading-relaxed">
                        {member.bio}
                    </p>
                )}

                {/* Expertise Tags */}
                {expertise.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="text-sm font-medium text-foreground">Expertise:</h4>
                        <div className="flex flex-wrap gap-1">
                            {expertise.map((skill, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                    {skill}
                                </Badge>
                            ))}
                        </div>
                    </div>
                )}

                {/* Contact Actions */}
                {showContact && (
                    <div className="pt-4 border-t space-y-3">
                        <div className="flex justify-center space-x-2">
                            {/* Email */}
                            {member.user?.email && (
                                <Button size="sm" variant="outline" asChild>
                                    <a href={`mailto:${member.user.email}`}>
                                        <Mail className="h-4 w-4 mr-1" />
                                        Email
                                    </a>
                                </Button>
                            )}
                            
                            {/* LinkedIn - placeholder */}
                            <Button size="sm" variant="outline" asChild>
                                <a 
                                    href={`https://linkedin.com/in/${member.name.toLowerCase().replace(' ', '-')}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <Linkedin className="h-4 w-4 mr-1" />
                                    LinkedIn
                                </a>
                            </Button>
                        </div>
                        
                        <Button className="w-full" variant="default" asChild>
                            <Link href="/book-consultation">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Book Consultation
                            </Link>
                        </Button>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}

interface TeamGridProps {
    members: TeamMember[];
    className?: string;
    showContact?: boolean;
}

export function TeamGrid({ members, className = '', showContact = true }: TeamGridProps) {
    // Filter only active members and sort by sort_order
    const activeMembers = members
        .filter(member => member.is_active)
        .sort((a, b) => a.sort_order - b.sort_order);

    return (
        <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}>
            {activeMembers.map((member) => (
                <TeamMemberCard
                    key={member.id}
                    member={member}
                    showContact={showContact}
                />
            ))}
        </div>
    );
}
