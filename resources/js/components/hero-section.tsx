import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link } from '@inertiajs/react';
import { ArrowRight, Play, Star, TrendingUp, Users, Zap } from 'lucide-react';

interface HeroSectionProps {
    title?: string;
    subtitle?: string;
    description?: string;
    primaryCta?: {
        text: string;
        href: string;
    };
    secondaryCta?: {
        text: string;
        href: string;
    };
    badge?: string;
    stats?: Array<{
        value: string;
        label: string;
        icon?: React.ComponentType<Record<string, unknown>>;
    }>;
    className?: string;
}

export function HeroSection({
    title = "Maximize Your Meta Ads ROI with Expert Analytics",
    subtitle = "ConvertOKit",
    description = "Transform your digital marketing with professional Meta advertising management, advanced tracking implementation, and data-driven optimization strategies.",
    primaryCta = {
        text: "Book Free Consultation",
        href: "/book-consultation"
    },
    secondaryCta = {
        text: "View Our Services",
        href: "/services"
    },
    badge = "🚀 Trusted by 500+ Businesses",
    stats = [
        { value: "300%", label: "Avg ROI Increase", icon: TrendingUp },
        { value: "500+", label: "Happy Clients", icon: Users },
        { value: "99%", label: "Tracking Accuracy", icon: Zap },
    ],
    className = ""
}: HeroSectionProps) {
    return (
        <section className={`relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 ${className}`}>
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-grid-black/[0.02] bg-[size:60px_60px]" />
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5" />
            
            <div className="container relative mx-auto px-4 py-24 lg:py-32">
                <div className="mx-auto max-w-4xl text-center">
                    {/* Badge */}
                    {badge && (
                        <Badge variant="secondary" className="mb-6 px-4 py-2 text-sm">
                            {badge}
                        </Badge>
                    )}

                    {/* Subtitle */}
                    {subtitle && (
                        <p className="mb-4 text-lg font-medium text-primary">
                            {subtitle}
                        </p>
                    )}

                    {/* Main Title */}
                    <h1 className="mb-6 text-4xl font-bold tracking-tight text-foreground sm:text-5xl lg:text-6xl">
                        {title}
                    </h1>

                    {/* Description */}
                    <p className="mb-8 text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                        {description}
                    </p>

                    {/* CTA Buttons */}
                    <div className="mb-12 flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <Button size="lg" className="px-8 py-3 text-lg" asChild>
                            <Link href={primaryCta.href}>
                                {primaryCta.text}
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="px-8 py-3 text-lg" asChild>
                            <Link href={secondaryCta.href}>
                                <Play className="mr-2 h-5 w-5" />
                                {secondaryCta.text}
                            </Link>
                        </Button>
                    </div>

                    {/* Stats */}
                    {stats && stats.length > 0 && (
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto">
                            {stats.map((stat, index) => (
                                <div key={index} className="text-center">
                                    <div className="flex items-center justify-center mb-2">
                                        {stat.icon && <stat.icon className="h-6 w-6 text-primary mr-2" />}
                                        <span className="text-3xl font-bold text-foreground">
                                            {stat.value}
                                        </span>
                                    </div>
                                    <p className="text-sm text-muted-foreground font-medium">
                                        {stat.label}
                                    </p>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Trust Indicators */}
                <div className="mt-16 text-center">
                    <p className="text-sm text-muted-foreground mb-6">Trusted by leading brands worldwide</p>
                    <div className="flex items-center justify-center space-x-8 opacity-60">
                        {/* Placeholder for client logos */}
                        <div className="flex items-center space-x-1">
                            <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                            <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                            <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                            <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                            <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                            <span className="ml-2 text-sm font-medium">4.9/5 Client Rating</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
