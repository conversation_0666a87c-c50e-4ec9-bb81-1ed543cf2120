import { Button } from '@/components/ui/button';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList, NavigationMenuLink, navigationMenuTriggerStyle } from '@/components/ui/navigation-menu';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { Menu, Phone, Mail } from 'lucide-react';
import { useState } from 'react';
import AppLogoIcon from './app-logo-icon';

const publicNavItems = [
    { title: 'Home', href: '/' },
    { title: 'About', href: '/about' },
    { title: 'Services', href: '/services' },
    { title: 'Team', href: '/team' },
    { title: 'Blog', href: '/blog' },
    { title: 'Contact', href: '/contact' },
];

export function PublicHeader() {
    const { auth } = usePage<SharedData>().props;
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    return (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container mx-auto flex h-16 items-center justify-between px-4">
                {/* Logo */}
                <Link href="/" className="flex items-center space-x-2">
                    <AppLogoIcon className="h-8 w-8" />
                    <span className="hidden font-bold sm:inline-block">ConvertOKit</span>
                </Link>

                {/* Desktop Navigation */}
                <NavigationMenu className="hidden md:flex">
                    <NavigationMenuList>
                        {publicNavItems.map((item) => (
                            <NavigationMenuItem key={item.href}>
                                <NavigationMenuLink
                                    href={item.href}
                                    className={navigationMenuTriggerStyle()}
                                >
                                    {item.title}
                                </NavigationMenuLink>
                            </NavigationMenuItem>
                        ))}
                    </NavigationMenuList>
                </NavigationMenu>

                {/* Right Side Actions */}
                <div className="flex items-center space-x-4">
                    {/* Contact Info - Hidden on mobile */}
                    <div className="hidden lg:flex items-center space-x-4 text-sm text-muted-foreground">
                        <a href="tel:+1234567890" className="flex items-center space-x-1 hover:text-foreground">
                            <Phone className="h-4 w-4" />
                            <span>+1 (234) 567-890</span>
                        </a>
                        <a href="mailto:<EMAIL>" className="flex items-center space-x-1 hover:text-foreground">
                            <Mail className="h-4 w-4" />
                            <span><EMAIL></span>
                        </a>
                    </div>

                    {/* Auth Buttons */}
                    {auth?.user ? (
                        <div className="hidden md:flex items-center space-x-2">
                            <Button variant="outline" asChild>
                                <Link href="/dashboard">Dashboard</Link>
                            </Button>
                        </div>
                    ) : (
                        <div className="hidden md:flex items-center space-x-2">
                            <Button variant="outline" asChild>
                                <Link href="/login">Login</Link>
                            </Button>
                            <Button asChild>
                                <Link href="/book-consultation">Book Consultation</Link>
                            </Button>
                        </div>
                    )}

                    {/* Mobile Menu */}
                    <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                        <SheetTrigger asChild>
                            <Button variant="ghost" size="icon" className="md:hidden">
                                <Menu className="h-5 w-5" />
                                <span className="sr-only">Toggle menu</span>
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                            <SheetHeader>
                                <SheetTitle className="flex items-center space-x-2">
                                    <AppLogoIcon className="h-6 w-6" />
                                    <span>ConvertOKit</span>
                                </SheetTitle>
                            </SheetHeader>
                            <div className="mt-6 flex flex-col space-y-4">
                                {publicNavItems.map((item) => (
                                    <Link
                                        key={item.href}
                                        href={item.href}
                                        className="text-lg font-medium hover:text-primary"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        {item.title}
                                    </Link>
                                ))}
                                <div className="border-t pt-4">
                                    {auth?.user ? (
                                        <Button className="w-full" asChild>
                                            <Link href="/dashboard">Dashboard</Link>
                                        </Button>
                                    ) : (
                                        <div className="space-y-2">
                                            <Button variant="outline" className="w-full" asChild>
                                                <Link href="/login">Login</Link>
                                            </Button>
                                            <Button className="w-full" asChild>
                                                <Link href="/book-consultation">Book Consultation</Link>
                                            </Button>
                                        </div>
                                    )}
                                </div>
                                <div className="border-t pt-4 space-y-2 text-sm text-muted-foreground">
                                    <a href="tel:+1234567890" className="flex items-center space-x-2 hover:text-foreground">
                                        <Phone className="h-4 w-4" />
                                        <span>+1 (234) 567-890</span>
                                    </a>
                                    <a href="mailto:<EMAIL>" className="flex items-center space-x-2 hover:text-foreground">
                                        <Mail className="h-4 w-4" />
                                        <span><EMAIL></span>
                                    </a>
                                </div>
                            </div>
                        </SheetContent>
                    </Sheet>
                </div>
            </div>
        </header>
    );
}
