import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import useLocalStorage from '../use-local-storage';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('useLocalStorage Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns initial value when localStorage is empty', () => {
    localStorageMock.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'));
    
    expect(result.current[0]).toBe('initial-value');
    expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key');
  });

  it('returns stored value from localStorage', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify('stored-value'));
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'));
    
    expect(result.current[0]).toBe('stored-value');
  });

  it('stores value in localStorage when setValue is called', () => {
    localStorageMock.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'));
    
    act(() => {
      result.current[1]('new-value');
    });
    
    expect(result.current[0]).toBe('new-value');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('test-key', JSON.stringify('new-value'));
  });

  it('handles function updates', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(5));
    
    const { result } = renderHook(() => useLocalStorage('test-key', 0));
    
    act(() => {
      result.current[1]((prev: number) => prev + 1);
    });
    
    expect(result.current[0]).toBe(6);
    expect(localStorageMock.setItem).toHaveBeenCalledWith('test-key', JSON.stringify(6));
  });

  it('handles complex objects', () => {
    const complexObject = { name: 'John', age: 30, hobbies: ['reading', 'coding'] };
    localStorageMock.getItem.mockReturnValue(JSON.stringify(complexObject));
    
    const { result } = renderHook(() => useLocalStorage('user', {}));
    
    expect(result.current[0]).toEqual(complexObject);
  });

  it('handles localStorage errors gracefully', () => {
    localStorageMock.getItem.mockImplementation(() => {
      throw new Error('localStorage error');
    });
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'fallback'));
    
    expect(result.current[0]).toBe('fallback');
  });

  it('handles setItem errors gracefully', () => {
    localStorageMock.getItem.mockReturnValue(null);
    localStorageMock.setItem.mockImplementation(() => {
      throw new Error('localStorage setItem error');
    });
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));
    
    // Should not throw error
    act(() => {
      result.current[1]('new-value');
    });
    
    // Value should still be updated in state
    expect(result.current[0]).toBe('new-value');
  });

  it('handles invalid JSON in localStorage', () => {
    localStorageMock.getItem.mockReturnValue('invalid-json{');
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'fallback'));
    
    expect(result.current[0]).toBe('fallback');
  });

  it('works with different data types', () => {
    // Test with number
    localStorageMock.getItem.mockReturnValue(JSON.stringify(42));
    const { result: numberResult } = renderHook(() => useLocalStorage('number-key', 0));
    expect(numberResult.current[0]).toBe(42);
    
    // Test with boolean
    localStorageMock.getItem.mockReturnValue(JSON.stringify(true));
    const { result: booleanResult } = renderHook(() => useLocalStorage('boolean-key', false));
    expect(booleanResult.current[0]).toBe(true);
    
    // Test with array
    localStorageMock.getItem.mockReturnValue(JSON.stringify([1, 2, 3]));
    const { result: arrayResult } = renderHook(() => useLocalStorage('array-key', []));
    expect(arrayResult.current[0]).toEqual([1, 2, 3]);
  });

  it('handles server-side rendering (no localStorage)', () => {
    // Mock localStorage to throw an error (simulating SSR or private browsing)
    const originalLocalStorage = global.window.localStorage;
    Object.defineProperty(global.window, 'localStorage', {
      get() {
        throw new Error('localStorage is not available');
      },
      configurable: true,
    });

    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

    expect(result.current[0]).toBe('initial');

    // Restore localStorage
    Object.defineProperty(global.window, 'localStorage', {
      value: originalLocalStorage,
      writable: true,
      configurable: true,
    });
  });
});
