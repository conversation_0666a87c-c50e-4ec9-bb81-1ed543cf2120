import { BlogPostGrid } from '@/components/blog-post-card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import PublicLayout from '@/layouts/public-layout';
import { type BlogPost, type Category } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Search, ArrowRight } from 'lucide-react';
import { useState } from 'react';

interface BlogProps {
    blogPosts?: BlogPost[];
    categories?: Category[];
}

export default function Blog({ blogPosts = [], categories = [] }: BlogProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');

    // Filter posts based on search and category
    const filteredPosts = blogPosts.filter(post => {
        const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || 
                              (post.category && post.category.name === selectedCategory);
        return matchesSearch && matchesCategory;
    });

    return (
        <PublicLayout>
            <Head title="Blog - ConvertOKit" />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 px-4 py-2">
                            Blog & Resources
                        </Badge>
                        <h1 className="text-4xl font-bold mb-6 sm:text-5xl">
                            Digital Marketing Insights
                        </h1>
                        <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                            Stay updated with the latest strategies, tips, and insights in Meta advertising, 
                            web analytics, and digital marketing optimization.
                        </p>
                    </div>
                </div>
            </section>

            {/* Search and Filter */}
            <section className="py-8 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="flex flex-col md:flex-row gap-4">
                            {/* Search */}
                            <div className="flex-1 relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    type="text"
                                    placeholder="Search articles..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            
                            {/* Category Filter */}
                            <div className="md:w-48">
                                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Categories" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Categories</SelectItem>
                                        {categories.map((category) => (
                                            <SelectItem key={category.id} value={category.name}>
                                                {category.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Blog Posts */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        {filteredPosts.length > 0 ? (
                            <>
                                <BlogPostGrid posts={filteredPosts} />
                                
                                {/* Load More Button - placeholder for pagination */}
                                {filteredPosts.length >= 9 && (
                                    <div className="text-center mt-12">
                                        <Button variant="outline" size="lg">
                                            Load More Articles
                                        </Button>
                                    </div>
                                )}
                            </>
                        ) : blogPosts.length === 0 ? (
                            <div className="text-center py-12">
                                <h3 className="text-2xl font-semibold mb-4">Coming Soon</h3>
                                <p className="text-lg text-muted-foreground mb-8">
                                    We're working on creating valuable content for you. 
                                    Check back soon for the latest insights and strategies.
                                </p>
                                <Button asChild>
                                    <Link href="/contact">
                                        Get Notified When We Publish
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <h3 className="text-2xl font-semibold mb-4">No Articles Found</h3>
                                <p className="text-lg text-muted-foreground mb-8">
                                    Try adjusting your search terms or category filter.
                                </p>
                                <Button 
                                    variant="outline" 
                                    onClick={() => {
                                        setSearchTerm('');
                                        setSelectedCategory('all');
                                    }}
                                >
                                    Clear Filters
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </section>

            {/* Newsletter CTA */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Subscribe to our newsletter and get the latest digital marketing insights delivered to your inbox
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                        <Input 
                            type="email" 
                            placeholder="Enter your email" 
                            className="bg-background text-foreground"
                        />
                        <Button variant="secondary">
                            Subscribe
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
