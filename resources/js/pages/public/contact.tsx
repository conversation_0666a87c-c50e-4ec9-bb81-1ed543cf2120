import { ContactForm } from '@/components/contact-form';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PublicLayout from '@/layouts/public-layout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Mail, Phone, MapPin, Clock, MessageCircle, Calendar } from 'lucide-react';

export default function Contact() {
    const contactMethods = [
        {
            icon: Mail,
            title: 'Email Us',
            description: 'Send us an email and we\'ll respond within 24 hours',
            value: '<EMAIL>',
            action: 'mailto:<EMAIL>'
        },
        {
            icon: Phone,
            title: 'Call Us',
            description: 'Speak directly with our team during business hours',
            value: '+1 (234) 567-890',
            action: 'tel:+1234567890'
        },
        {
            icon: Calendar,
            title: 'Book a Meeting',
            description: 'Schedule a free consultation to discuss your needs',
            value: 'Free Consultation',
            action: '/book-consultation'
        }
    ];

    const officeHours = [
        { day: 'Monday - Friday', hours: '9:00 AM - 6:00 PM EST' },
        { day: 'Saturday', hours: '10:00 AM - 4:00 PM EST' },
        { day: 'Sunday', hours: 'Closed' }
    ];

    const faqs = [
        {
            question: 'How quickly can you start working on my project?',
            answer: 'We typically begin new projects within 1-2 weeks of signing the agreement, depending on the scope and our current workload.'
        },
        {
            question: 'Do you work with businesses of all sizes?',
            answer: 'Yes, we work with businesses ranging from startups to enterprise companies. Our services are scalable to meet your specific needs and budget.'
        },
        {
            question: 'What platforms do you specialize in?',
            answer: 'We specialize in Meta (Facebook & Instagram) advertising, Google Analytics, Google Tag Manager, and various tracking implementations.'
        },
        {
            question: 'Do you provide ongoing support?',
            answer: 'Yes, we offer ongoing support and optimization services to ensure your campaigns and tracking continue to perform at their best.'
        }
    ];

    return (
        <PublicLayout>
            <Head title="Contact Us - ConvertOKit" />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 px-4 py-2">
                            Contact Us
                        </Badge>
                        <h1 className="text-4xl font-bold mb-6 sm:text-5xl">
                            Let's Discuss Your Project
                        </h1>
                        <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                            Ready to boost your digital marketing performance? Get in touch with our team 
                            and let's create a strategy that delivers real results for your business.
                        </p>
                    </div>
                </div>
            </section>

            {/* Contact Methods */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Get In Touch</h2>
                            <p className="text-lg text-muted-foreground">
                                Choose the method that works best for you
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                            {contactMethods.map((method, index) => (
                                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                                    <CardHeader>
                                        <div className="flex justify-center mb-4">
                                            <method.icon className="h-12 w-12 text-primary" />
                                        </div>
                                        <CardTitle>{method.title}</CardTitle>
                                        <CardDescription>{method.description}</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="font-semibold mb-4">{method.value}</p>
                                        <Button asChild>
                                            {method.action.startsWith('/') ? (
                                                <Link href={method.action}>Contact Now</Link>
                                            ) : (
                                                <a href={method.action}>Contact Now</a>
                                            )}
                                        </Button>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Contact Form and Info */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                            {/* Contact Form */}
                            <div className="lg:col-span-2">
                                <ContactForm />
                            </div>
                            
                            {/* Contact Information */}
                            <div className="space-y-6">
                                {/* Office Hours */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <Clock className="h-5 w-5 mr-2" />
                                            Office Hours
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        {officeHours.map((schedule, index) => (
                                            <div key={index} className="flex justify-between">
                                                <span className="text-muted-foreground">{schedule.day}</span>
                                                <span className="font-medium">{schedule.hours}</span>
                                            </div>
                                        ))}
                                    </CardContent>
                                </Card>

                                {/* Location */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <MapPin className="h-5 w-5 mr-2" />
                                            Location
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-muted-foreground mb-2">
                                            We operate remotely and serve clients globally
                                        </p>
                                        <p className="text-sm text-muted-foreground">
                                            Based in the United States with team members across multiple time zones
                                        </p>
                                    </CardContent>
                                </Card>

                                {/* Response Time */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <MessageCircle className="h-5 w-5 mr-2" />
                                            Response Time
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-muted-foreground mb-2">
                                            We typically respond to all inquiries within 24 hours
                                        </p>
                                        <p className="text-sm text-muted-foreground">
                                            For urgent matters, please call us directly during business hours
                                        </p>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ Section */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
                            <p className="text-lg text-muted-foreground">
                                Quick answers to common questions about our services
                            </p>
                        </div>
                        
                        <div className="space-y-6">
                            {faqs.map((faq, index) => (
                                <Card key={index}>
                                    <CardHeader>
                                        <CardTitle className="text-lg">{faq.question}</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-muted-foreground leading-relaxed">
                                            {faq.answer}
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                        
                        <div className="text-center mt-8">
                            <p className="text-muted-foreground mb-4">
                                Don't see your question answered here?
                            </p>
                            <Button variant="outline" asChild>
                                <Link href="/book-consultation">
                                    Schedule a Free Consultation
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let's discuss your digital marketing goals and create a strategy that works for your business
                    </p>
                    <Button size="lg" variant="secondary" asChild>
                        <Link href="/book-consultation">
                            Book Your Free Consultation
                        </Link>
                    </Button>
                </div>
            </section>
        </PublicLayout>
    );
}
