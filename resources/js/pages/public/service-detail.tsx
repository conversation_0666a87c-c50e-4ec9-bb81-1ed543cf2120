import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PublicLayout from '@/layouts/public-layout';
import { type Service } from '@/types';
import { <PERSON>, Link } from '@inertiajs/react';
import { ArrowRight, CheckCircle, Star, Clock, Users } from 'lucide-react';

interface ServiceDetailProps {
    slug: string;
    service?: Service;
}

export default function ServiceDetail({ service }: ServiceDetailProps) {
    // Mock service data for demonstration - this will be replaced with real data from backend
    const mockService: Service = {
        id: 1,
        title: 'Meta Ads Management',
        slug: 'meta-ads-management',
        description: 'Complete Facebook and Instagram advertising management with advanced targeting and optimization.',
        detailed_description: `
            <p>Our comprehensive Meta Ads Management service is designed to maximize your return on investment through strategic campaign planning, advanced targeting, and continuous optimization.</p>
            
            <h3>What's Included:</h3>
            <ul>
                <li>Initial account audit and strategy development</li>
                <li>Campaign setup and configuration</li>
                <li>Creative development and testing</li>
                <li>Advanced audience targeting and lookalike audiences</li>
                <li>Ongoing optimization and performance monitoring</li>
                <li>Monthly reporting and strategy calls</li>
            </ul>
            
            <h3>Our Process:</h3>
            <p>We start with a comprehensive audit of your current advertising efforts and business goals. From there, we develop a customized strategy that aligns with your objectives and budget.</p>
            
            <p>Our team continuously monitors and optimizes your campaigns to ensure peak performance, making data-driven adjustments to improve your return on ad spend.</p>
        `,
        features: [
            'Campaign Setup & Strategy',
            'Creative Development',
            'Advanced Targeting',
            'Performance Optimization',
            'Monthly Reporting',
            'A/B Testing',
            'Conversion Tracking',
            'Budget Management'
        ],
        price_range: '$2,000 - $5,000',
        category: 'Meta Advertising',
        is_active: true,
        sort_order: 1,
        meta_title: 'Meta Ads Management - Professional Facebook & Instagram Advertising',
        meta_description: 'Expert Meta advertising management to maximize your ROI with strategic campaigns, advanced targeting, and continuous optimization.',
        created_at: '2025-01-01T00:00:00.000Z',
        updated_at: '2025-01-01T00:00:00.000Z',
    };

    const displayService = service || mockService;

    const benefits = [
        {
            icon: Star,
            title: 'Expert Management',
            description: 'Certified Meta advertising specialists managing your campaigns'
        },
        {
            icon: CheckCircle,
            title: 'Proven Results',
            description: 'Average 300% increase in ROI for our clients'
        },
        {
            icon: Clock,
            title: 'Time Savings',
            description: 'Focus on your business while we handle your advertising'
        },
        {
            icon: Users,
            title: 'Dedicated Support',
            description: 'Direct access to your account manager and team'
        }
    ];

    const testimonials = [
        {
            quote: "ConvertOKit transformed our Meta advertising. Our ROAS increased by 400% in just 3 months.",
            author: "Sarah Johnson",
            company: "E-commerce Store Owner",
            rating: 5
        },
        {
            quote: "The team's expertise in Facebook advertising is unmatched. Highly recommend their services.",
            author: "Mike Chen",
            company: "SaaS Startup Founder",
            rating: 5
        }
    ];

    return (
        <PublicLayout>
            <Head 
                title={displayService.meta_title || `${displayService.title} - ConvertOKit`}
                description={displayService.meta_description || displayService.description}
            />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-8">
                            <Badge variant="secondary" className="mb-4">
                                {displayService.category}
                            </Badge>
                            <h1 className="text-4xl font-bold mb-4 sm:text-5xl">
                                {displayService.title}
                            </h1>
                            <p className="text-xl text-muted-foreground mb-6 leading-relaxed">
                                {displayService.description}
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Button size="lg" asChild>
                                    <Link href="/book-consultation">
                                        Book Consultation
                                        <ArrowRight className="ml-2 h-5 w-5" />
                                    </Link>
                                </Button>
                                <Button size="lg" variant="outline" asChild>
                                    <Link href="/contact">
                                        Get Quote
                                    </Link>
                                </Button>
                            </div>
                        </div>
                        
                        <div className="text-center">
                            <p className="text-2xl font-semibold text-primary mb-2">
                                {displayService.price_range}
                            </p>
                            <p className="text-muted-foreground">
                                Monthly investment
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <h2 className="text-3xl font-bold text-center mb-12">What's Included</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {displayService.features.map((feature, index) => (
                                <div key={index} className="flex items-center space-x-3">
                                    <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                                    <span className="text-foreground">{feature}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Benefits Section */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <h2 className="text-3xl font-bold text-center mb-12">Why Choose This Service</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {benefits.map((benefit, index) => (
                                <Card key={index} className="text-center border-0 shadow-md">
                                    <CardHeader>
                                        <div className="flex justify-center mb-4">
                                            <benefit.icon className="h-12 w-12 text-primary" />
                                        </div>
                                        <CardTitle className="text-lg">{benefit.title}</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-sm text-muted-foreground leading-relaxed">
                                            {benefit.description}
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Detailed Description */}
            {displayService.detailed_description && (
                <section className="py-16">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto">
                            <div 
                                className="prose prose-lg max-w-none"
                                dangerouslySetInnerHTML={{ __html: displayService.detailed_description }}
                            />
                        </div>
                    </div>
                </section>
            )}

            {/* Testimonials */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <h2 className="text-3xl font-bold text-center mb-12">What Our Clients Say</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {testimonials.map((testimonial, index) => (
                                <Card key={index} className="border-0 shadow-md">
                                    <CardContent className="p-6">
                                        <div className="flex mb-4">
                                            {[...Array(testimonial.rating)].map((_, i) => (
                                                <Star key={i} className="h-5 w-5 text-yellow-500 fill-current" />
                                            ))}
                                        </div>
                                        <blockquote className="text-muted-foreground mb-4 leading-relaxed">
                                            "{testimonial.quote}"
                                        </blockquote>
                                        <div className="border-t pt-4">
                                            <p className="font-semibold">{testimonial.author}</p>
                                            <p className="text-sm text-muted-foreground">{testimonial.company}</p>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let's discuss how {displayService.title.toLowerCase()} can help grow your business
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                            <Link href="/services">
                                View All Services
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
