import { ServiceGrid } from '@/components/service-card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/public-layout';
import { type Service } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowRight, CheckCircle, Target, TrendingUp, Zap } from 'lucide-react';

interface ServicesProps {
    services?: Service[];
}

export default function Services({ services = [] }: ServicesProps) {
    // Mock data for demonstration - this will be replaced with real data from backend
    const mockServices: Service[] = [
        {
            id: 1,
            title: 'Meta Ads Management',
            slug: 'meta-ads-management',
            description: 'Complete Facebook and Instagram advertising management with advanced targeting and optimization.',
            detailed_description: '',
            features: ['Campaign Setup & Strategy', 'Creative Development', 'Advanced Targeting', 'Performance Optimization', 'Monthly Reporting', 'A/B Testing'],
            price_range: '$2,000 - $5,000',
            category: 'Meta Advertising',
            is_active: true,
            sort_order: 1,
            meta_title: '',
            meta_description: '',
            created_at: '',
            updated_at: ''
        },
        {
            id: 2,
            title: 'Facebook Pixel Setup',
            slug: 'facebook-pixel-setup',
            description: 'Professional Facebook Pixel implementation with conversion tracking and server-side events.',
            detailed_description: '',
            features: ['Pixel Installation', 'Event Configuration', 'Conversion API Setup', 'Testing & Validation', 'Documentation', 'Training'],
            price_range: '$500 - $1,500',
            category: 'Tracking & Analytics',
            is_active: true,
            sort_order: 2,
            meta_title: '',
            meta_description: '',
            created_at: '',
            updated_at: ''
        },
        {
            id: 3,
            title: 'Google Analytics Setup',
            slug: 'google-analytics-setup',
            description: 'Complete GA4 setup with enhanced eCommerce tracking and custom reporting.',
            detailed_description: '',
            features: ['GA4 Configuration', 'Enhanced eCommerce', 'Custom Events', 'Goal Setup', 'Dashboard Creation', 'Training'],
            price_range: '$800 - $2,000',
            category: 'Tracking & Analytics',
            is_active: true,
            sort_order: 3,
            meta_title: '',
            meta_description: '',
            created_at: '',
            updated_at: ''
        },
        {
            id: 4,
            title: 'Conversion Tracking Audit',
            slug: 'conversion-tracking-audit',
            description: 'Comprehensive audit of your current tracking setup with recommendations for improvement.',
            detailed_description: '',
            features: ['Tracking Analysis', 'Data Quality Assessment', 'Recommendations Report', 'Implementation Roadmap', 'Priority Matrix'],
            price_range: '$300 - $800',
            category: 'Consulting',
            is_active: true,
            sort_order: 4,
            meta_title: '',
            meta_description: '',
            created_at: '',
            updated_at: ''
        },
        {
            id: 5,
            title: 'Google Tag Manager Setup',
            slug: 'google-tag-manager-setup',
            description: 'Professional GTM implementation with advanced tracking and data layer configuration.',
            detailed_description: '',
            features: ['GTM Container Setup', 'Data Layer Implementation', 'Tag Configuration', 'Trigger Setup', 'Testing & Debugging'],
            price_range: '$600 - $1,800',
            category: 'Tracking & Analytics',
            is_active: true,
            sort_order: 5,
            meta_title: '',
            meta_description: '',
            created_at: '',
            updated_at: ''
        },
        {
            id: 6,
            title: 'Digital Marketing Consultation',
            slug: 'digital-marketing-consultation',
            description: 'Strategic consultation to optimize your digital marketing efforts and improve ROI.',
            detailed_description: '',
            features: ['Strategy Review', 'Performance Analysis', 'Recommendations', 'Action Plan', 'Follow-up Session'],
            price_range: '$200 - $500',
            category: 'Consulting',
            is_active: true,
            sort_order: 6,
            meta_title: '',
            meta_description: '',
            created_at: '',
            updated_at: ''
        }
    ];

    const allServices = services.length > 0 ? services : mockServices;
    
    // Group services by category
    const servicesByCategory = allServices.reduce((acc, service) => {
        const category = service.category || 'Other';
        if (!acc[category]) {
            acc[category] = [];
        }
        acc[category].push(service);
        return acc;
    }, {} as Record<string, Service[]>);

    const benefits = [
        {
            icon: Target,
            title: 'Proven Results',
            description: 'Our strategies have helped clients achieve an average 300% increase in ROI'
        },
        {
            icon: Zap,
            title: 'Advanced Tracking',
            description: '99% tracking accuracy with server-side implementation and data validation'
        },
        {
            icon: TrendingUp,
            title: 'Continuous Optimization',
            description: 'Ongoing monitoring and optimization to ensure peak performance'
        },
        {
            icon: CheckCircle,
            title: 'Full Transparency',
            description: 'Detailed reporting and clear communication throughout the process'
        }
    ];

    return (
        <PublicLayout>
            <Head title="Our Services - ConvertOKit" />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 px-4 py-2">
                            Our Services
                        </Badge>
                        <h1 className="text-4xl font-bold mb-6 sm:text-5xl">
                            Expert Meta Ads & Analytics Services
                        </h1>
                        <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                            Comprehensive digital marketing solutions designed to maximize your ROI 
                            and provide accurate tracking of your marketing performance.
                        </p>
                        <Button size="lg" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>

            {/* Benefits Section */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Why Choose ConvertOKit?</h2>
                            <p className="text-lg text-muted-foreground">
                                We deliver results through expertise, transparency, and proven methodologies
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {benefits.map((benefit, index) => (
                                <Card key={index} className="text-center border-0 shadow-md">
                                    <CardHeader>
                                        <div className="flex justify-center mb-4">
                                            <benefit.icon className="h-12 w-12 text-primary" />
                                        </div>
                                        <CardTitle className="text-lg">{benefit.title}</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-sm text-muted-foreground leading-relaxed">
                                            {benefit.description}
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Services by Category */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        {Object.entries(servicesByCategory).map(([category, categoryServices], index) => (
                            <div key={category} className={index > 0 ? 'mt-16' : ''}>
                                <div className="text-center mb-12">
                                    <h2 className="text-3xl font-bold mb-4">{category}</h2>
                                    <p className="text-lg text-muted-foreground">
                                        {category === 'Meta Advertising' && 'Professional Facebook and Instagram advertising management'}
                                        {category === 'Tracking & Analytics' && 'Advanced tracking implementation and analytics setup'}
                                        {category === 'Consulting' && 'Strategic guidance and optimization recommendations'}
                                        {category === 'Other' && 'Additional services to support your digital marketing goals'}
                                    </p>
                                </div>
                                <ServiceGrid 
                                    services={categoryServices} 
                                    featuredServiceId={category === 'Meta Advertising' ? 1 : undefined}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Process Section */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Our Process</h2>
                            <p className="text-lg text-muted-foreground">
                                A proven methodology that delivers consistent results
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div className="text-center">
                                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                                    1
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Discovery & Analysis</h3>
                                <p className="text-muted-foreground">
                                    We analyze your current setup, understand your goals, and identify opportunities for improvement.
                                </p>
                            </div>
                            <div className="text-center">
                                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                                    2
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Strategy & Implementation</h3>
                                <p className="text-muted-foreground">
                                    We develop a customized strategy and implement solutions using industry best practices.
                                </p>
                            </div>
                            <div className="text-center">
                                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                                    3
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Optimization & Reporting</h3>
                                <p className="text-muted-foreground">
                                    We continuously monitor performance and provide detailed reports with actionable insights.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let's discuss your specific needs and create a customized solution for your business
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                            <Link href="/contact">
                                Contact Us
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
