import { ContactForm } from '@/components/contact-form';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PublicLayout from '@/layouts/public-layout';
import { Head } from '@inertiajs/react';
import { CheckCircle, Clock, Users, Zap } from 'lucide-react';

export default function BookConsultation() {
    const benefits = [
        {
            icon: CheckCircle,
            title: 'Free 30-Minute Session',
            description: 'No cost, no obligation consultation to discuss your needs'
        },
        {
            icon: Users,
            title: 'Expert Guidance',
            description: 'Get advice from certified Meta and analytics specialists'
        },
        {
            icon: Zap,
            title: 'Actionable Insights',
            description: 'Walk away with specific recommendations for your business'
        },
        {
            icon: Clock,
            title: 'Quick Turnaround',
            description: 'We typically respond within 24 hours to schedule your call'
        }
    ];

    const whatToExpect = [
        {
            step: 1,
            title: 'Discovery',
            description: 'We\'ll learn about your business, current marketing efforts, and goals'
        },
        {
            step: 2,
            title: 'Analysis',
            description: 'Quick review of your current setup and identification of opportunities'
        },
        {
            step: 3,
            title: 'Recommendations',
            description: 'Specific, actionable advice tailored to your business needs'
        },
        {
            step: 4,
            title: 'Next Steps',
            description: 'Clear roadmap for implementation, whether with us or on your own'
        }
    ];

    const testimonials = [
        {
            quote: "The consultation was incredibly valuable. They identified issues with our tracking that we didn't even know existed.",
            author: "Sarah Johnson",
            company: "E-commerce Store Owner"
        },
        {
            quote: "In just 30 minutes, they provided more actionable insights than our previous agency did in months.",
            author: "Mike Chen",
            company: "SaaS Startup Founder"
        },
        {
            quote: "Professional, knowledgeable, and genuinely helpful. The consultation led to a 200% improvement in our ad performance.",
            author: "Lisa Rodriguez",
            company: "Digital Marketing Manager"
        }
    ];

    return (
        <PublicLayout>
            <Head title="Book Free Consultation - ConvertOKit" />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 px-4 py-2">
                            Free Consultation
                        </Badge>
                        <h1 className="text-4xl font-bold mb-6 sm:text-5xl">
                            Book Your Free 30-Minute Consultation
                        </h1>
                        <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                            Get expert advice on your Meta advertising and analytics setup. 
                            No cost, no obligation - just valuable insights to help your business grow.
                        </p>
                    </div>
                </div>
            </section>

            {/* Benefits */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Why Book a Consultation?</h2>
                            <p className="text-lg text-muted-foreground">
                                Get personalized insights and recommendations from our experts
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {benefits.map((benefit, index) => (
                                <Card key={index} className="text-center border-0 shadow-md">
                                    <CardHeader>
                                        <div className="flex justify-center mb-4">
                                            <benefit.icon className="h-12 w-12 text-primary" />
                                        </div>
                                        <CardTitle className="text-lg">{benefit.title}</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-sm text-muted-foreground leading-relaxed">
                                            {benefit.description}
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* What to Expect */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">What to Expect</h2>
                            <p className="text-lg text-muted-foreground">
                                Here's how our consultation process works
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {whatToExpect.map((item, index) => (
                                <div key={index} className="flex items-start space-x-4">
                                    <div className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-lg font-bold flex-shrink-0">
                                        {item.step}
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
                                        <p className="text-muted-foreground leading-relaxed">
                                            {item.description}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Booking Form */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Schedule Your Consultation</h2>
                            <p className="text-lg text-muted-foreground">
                                Fill out the form below and we'll get back to you within 24 hours
                            </p>
                        </div>
                        
                        <ContactForm 
                            title="Book Your Free Consultation"
                            description="Tell us about your business and what you'd like to discuss during the consultation."
                            showServiceSelection={true}
                        />
                    </div>
                </div>
            </section>

            {/* Testimonials */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">What Our Clients Say</h2>
                            <p className="text-lg text-muted-foreground">
                                Hear from businesses that have benefited from our consultations
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {testimonials.map((testimonial, index) => (
                                <Card key={index} className="border-0 shadow-md">
                                    <CardContent className="p-6">
                                        <blockquote className="text-muted-foreground mb-4 leading-relaxed">
                                            "{testimonial.quote}"
                                        </blockquote>
                                        <div className="border-t pt-4">
                                            <p className="font-semibold">{testimonial.author}</p>
                                            <p className="text-sm text-muted-foreground">{testimonial.company}</p>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
                        </div>
                        
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg">Is the consultation really free?</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground">
                                        Yes, absolutely! We offer a genuine 30-minute consultation with no cost and no obligation. 
                                        Our goal is to provide value and help you understand how to improve your digital marketing.
                                    </p>
                                </CardContent>
                            </Card>
                            
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg">What should I prepare for the consultation?</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground">
                                        Come prepared to discuss your business goals, current marketing efforts, and any specific 
                                        challenges you're facing. If you have access to your analytics or ad accounts, that's helpful but not required.
                                    </p>
                                </CardContent>
                            </Card>
                            
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg">Will you try to sell me something?</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground">
                                        Our focus is on providing value and helping you succeed. If our services are a good fit, 
                                        we'll discuss how we can help, but there's no pressure or obligation to work with us.
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
