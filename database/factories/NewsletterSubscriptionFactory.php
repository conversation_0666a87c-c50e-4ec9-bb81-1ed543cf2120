<?php

namespace Database\Factories;

use App\Models\NewsletterSubscription;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class NewsletterSubscriptionFactory extends Factory
{
    protected $model = NewsletterSubscription::class;

    public function definition(): array
    {
        return [
            'email' => $this->faker->unique()->safeEmail(),
            'name' => $this->faker->optional()->name(),
            'status' => 'active',
            'preferences' => $this->faker->randomElements(['marketing', 'updates', 'newsletters'], 2),
            'subscribed_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'unsubscribed_at' => null,
            'unsubscribe_token' => \Illuminate\Support\Str::random(32),
            'source' => $this->faker->optional()->randomElement(['website', 'blog', 'social']),
        ];
    }

    public function inactive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'unsubscribed',
                'unsubscribed_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            ];
        });
    }

    public function active(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
                'unsubscribed_at' => null,
            ];
        });
    }

    public function bounced(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'bounced',
                'unsubscribed_at' => $this->faker->dateTimeBetween('-3 months', 'now'),
            ];
        });
    }
}
