<?php

namespace Database\Factories;

use App\Models\Consultation;
use App\Models\User;
use App\Models\Service;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Consultation>
 */
class ConsultationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Consultation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'service_id' => Service::factory(),
            'consultation_date' => fake()->dateTimeBetween('+1 day', '+30 days'),
            'duration' => fake()->randomElement([30, 60, 90, 120]),
            'status' => fake()->randomElement(['pending', 'confirmed', 'completed', 'cancelled']),
            'notes' => fake()->optional()->paragraph(),
            'meeting_link' => fake()->optional()->url(),
            'payment_status' => fake()->randomElement(['pending', 'paid', 'refunded']),
            'payment_id' => fake()->optional()->regexify('[A-Z0-9]{10}'),
        ];
    }

    /**
     * Indicate that the consultation is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the consultation is confirmed.
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'meeting_link' => fake()->url(),
        ]);
    }

    /**
     * Indicate that the consultation is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'payment_status' => 'paid',
            'consultation_date' => fake()->dateTimeBetween('-30 days', '-1 day'),
        ]);
    }

    /**
     * Indicate that the consultation is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'payment_status' => fake()->randomElement(['pending', 'refunded']),
        ]);
    }

    /**
     * Set a specific consultation date.
     */
    public function scheduledFor(Carbon $dateTime): static
    {
        return $this->state(fn (array $attributes) => [
            'consultation_date' => $dateTime,
        ]);
    }

    /**
     * Set a specific duration.
     */
    public function withDuration(int $minutes): static
    {
        return $this->state(fn (array $attributes) => [
            'duration' => $minutes,
        ]);
    }
}
