<?php

namespace Database\Factories;

use App\Models\ContactSubmission;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ContactSubmissionFactory extends Factory
{
    protected $model = ContactSubmission::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'company' => $this->faker->company(),
            'subject' => $this->faker->sentence(),
            'message' => $this->faker->paragraph(3),
            'status' => $this->faker->randomElement(['new', 'read', 'replied']),
            'admin_notes' => $this->faker->optional()->paragraph(),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
        ];
    }

    public function newStatus(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'new',
            ];
        });
    }

    public function read(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'read',
            ];
        });
    }
}
