<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TeamMember>
 */
class TeamMemberFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => \App\Models\User::factory(),
            'name' => fake()->name(),
            'position' => fake()->jobTitle(),
            'bio' => fake()->paragraph(),
            'avatar' => fake()->optional()->imageUrl(200, 200, 'people'),
            'expertise' => fake()->randomElements([
                'PHP', 'Laravel', 'React', 'JavaScript', 'TypeScript',
                'Google Ads', 'Facebook Ads', 'Analytics', 'SEO', 'Marketing'
            ], fake()->numberBetween(2, 5)),
            'is_active' => true,
            'sort_order' => fake()->numberBetween(0, 100),
        ];
    }
}
