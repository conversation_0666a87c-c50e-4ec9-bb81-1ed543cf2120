<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Service>
 */
class ServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = fake()->sentence(3);
        return [
            'title' => $title,
            'slug' => \Illuminate\Support\Str::slug($title),
            'description' => fake()->paragraph(),
            'detailed_description' => fake()->paragraphs(3, true),
            'features' => [
                fake()->sentence(),
                fake()->sentence(),
                fake()->sentence(),
            ],
            'price_range' => fake()->randomElement(['$500-$1000', '$1000-$2500', '$2500-$5000', 'Custom Quote']),
            'category' => fake()->randomElement(['Meta Ads', 'Analytics', 'Tracking', 'Consultation']),
            'is_active' => true,
            'sort_order' => fake()->numberBetween(0, 100),
            'meta_title' => $title,
            'meta_description' => fake()->sentence(),
        ];
    }
}
