<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Yearon Suraiya',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'phone' => '+1234567890',
                'company' => 'ConvertoKit',
                'email_verified_at' => now(),
            ]
        );

        // Create a test client user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Client',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'client',
                'company' => 'Example Corp',
                'email_verified_at' => now(),
            ]
        );
    }
}
