<?php

namespace Database\Seeders;

use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class TeamMembersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teamMembers = [
            [
                'name' => 'Jahidul Islam',
                'email' => '<EMAIL>',
                'position' => 'Google Ads Expert',
                'bio' => 'Experienced Google Ads specialist with over 5 years of experience in PPC campaign management and optimization.',
                'expertise' => ['Google Ads', 'PPC Management', 'Keyword Research', 'Campaign Optimization'],
                'sort_order' => 1,
            ],
            [
                'name' => 'Rasel Sikder',
                'email' => '<EMAIL>',
                'position' => 'Google Ads & Web Analytics Expert',
                'bio' => 'Dual expertise in Google Ads management and web analytics, specializing in conversion tracking and performance optimization.',
                'expertise' => ['Google Ads', 'Google Analytics', 'Conversion Tracking', 'Performance Analysis'],
                'sort_order' => 2,
            ],
            [
                'name' => 'Amit Roy',
                'email' => '<EMAIL>',
                'position' => 'Digital Marketing Specialist',
                'bio' => 'Digital marketing specialist focused on multi-channel campaign management and marketing automation.',
                'expertise' => ['Digital Marketing', 'Campaign Management', 'Marketing Automation', 'Social Media'],
                'sort_order' => 3,
            ],
            [
                'name' => 'Sohel Ahmed Sahil',
                'email' => '<EMAIL>',
                'position' => 'Marketing Analyst',
                'bio' => 'Data-driven marketing analyst specializing in performance metrics and conversion optimization strategies.',
                'expertise' => ['Marketing Analysis', 'Data Analytics', 'Conversion Optimization', 'Reporting'],
                'sort_order' => 4,
            ],
            [
                'name' => 'Firoz Anam',
                'email' => '<EMAIL>',
                'position' => 'Full Stack Developer',
                'bio' => 'Full stack developer responsible for technical implementation of tracking solutions and web development.',
                'expertise' => ['Full Stack Development', 'Tracking Implementation', 'Web Development', 'Technical Integration'],
                'sort_order' => 5,
            ],
        ];

        foreach ($teamMembers as $memberData) {
            // Create user account for team member
            $user = User::updateOrCreate(
                ['email' => $memberData['email']],
                [
                    'name' => $memberData['name'],
                    'email' => $memberData['email'],
                    'password' => Hash::make('password'),
                    'role' => 'team_member',
                    'email_verified_at' => now(),
                ]
            );

            // Create team member profile
            TeamMember::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'user_id' => $user->id,
                    'name' => $memberData['name'],
                    'position' => $memberData['position'],
                    'bio' => $memberData['bio'],
                    'expertise' => $memberData['expertise'],
                    'is_active' => true,
                    'sort_order' => $memberData['sort_order'],
                ]
            );
        }
    }
}
