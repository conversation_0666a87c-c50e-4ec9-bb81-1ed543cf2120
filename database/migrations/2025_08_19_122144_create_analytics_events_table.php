<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('analytics_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('session_id');
            $table->string('event_name');
            $table->json('event_parameters')->nullable();
            $table->text('page_url');
            $table->text('referrer')->nullable();
            $table->text('user_agent')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->boolean('pixel_fired')->default(false);
            $table->boolean('ga4_fired')->default(false);
            $table->decimal('conversion_value', 10, 2)->nullable();
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['session_id', 'created_at']);
            $table->index(['event_name', 'created_at']);
            $table->index(['user_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('analytics_events');
    }
};
