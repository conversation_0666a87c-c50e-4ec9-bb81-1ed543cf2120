# User Credentials - ConvertoKit Development

This document contains login credentials for all seeded user accounts in the ConvertoKit application. These accounts are created automatically when running `php artisan db:seed`.

---

## 🔐 Admin Account

### Yearon Suraiya (Admin)
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `admin`
- **Phone**: `+**********`
- **Company**: `ConvertoKit`
- **Status**: Email verified
- **Permissions**: Full admin access to all features

**Use Case**: Primary admin account for managing the entire application, content, users, and settings.

---

## 👤 Test Client Account

### Test Client
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `client`
- **Company**: `Example Corp`
- **Status**: Email verified
- **Permissions**: Client dashboard access, consultation booking, profile management

**Use Case**: Testing client-side functionality, consultation booking, and user experience.

---

## 👥 Team Member Accounts

All team members have the role `team_member` and password `password`. They have associated team member profiles with expertise and bio information.

### 1. <PERSON><PERSON><PERSON><PERSON> Islam - Google Ads Expert
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `team_member`
- **Position**: Google Ads Expert
- **Expertise**: Google Ads, PPC Management, Keyword Research, Campaign Optimization
- **Bio**: Experienced Google Ads specialist with over 5 years of experience in PPC campaign management and optimization.
- **Sort Order**: 1

### 2. Rasel Sikder - Google Ads & Web Analytics Expert
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `team_member`
- **Position**: Google Ads & Web Analytics Expert
- **Expertise**: Google Ads, Google Analytics, Conversion Tracking, Performance Analysis
- **Bio**: Dual expertise in Google Ads management and web analytics, specializing in conversion tracking and performance optimization.
- **Sort Order**: 2

### 3. Amit Roy - Digital Marketing Specialist
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `team_member`
- **Position**: Digital Marketing Specialist
- **Expertise**: Digital Marketing, Campaign Management, Marketing Automation, Social Media
- **Bio**: Digital marketing specialist focused on multi-channel campaign management and marketing automation.
- **Sort Order**: 3

### 4. Sohel Ahmed Sahil - Marketing Analyst
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `team_member`
- **Position**: Marketing Analyst
- **Expertise**: Marketing Analysis, Data Analytics, Conversion Optimization, Reporting
- **Bio**: Data-driven marketing analyst specializing in performance metrics and conversion optimization strategies.
- **Sort Order**: 4

### 5. Firoz Anam - Full Stack Developer
- **Email**: `<EMAIL>`
- **Password**: `password`
- **Role**: `team_member`
- **Position**: Full Stack Developer
- **Expertise**: Full Stack Development, Tracking Implementation, Web Development, Technical Integration
- **Bio**: Full stack developer responsible for technical implementation of tracking solutions and web development.
- **Sort Order**: 5

---

## 🔑 Quick Login Reference

| Role | Email | Password | Purpose |
|------|-------|----------|---------|
| Admin | `<EMAIL>` | `password` | Full admin access |
| Client | `<EMAIL>` | `password` | Client testing |
| Team Member | `<EMAIL>` | `password` | Google Ads expert |
| Team Member | `<EMAIL>` | `password` | Analytics expert |
| Team Member | `<EMAIL>` | `password` | Marketing specialist |
| Team Member | `<EMAIL>` | `password` | Marketing analyst |
| Team Member | `<EMAIL>` | `password` | Developer |

---

## 🚀 Login URLs

- **Login Page**: `/login`
- **Registration Page**: `/register`
- **Dashboard**: `/dashboard` (redirected after login)
- **Admin Panel**: `/admin` (admin role required)

---

## 📝 Notes

### Security Considerations
- **Development Only**: These credentials are for development and testing purposes only
- **Default Password**: All accounts use the default password `password`
- **Production**: Change all passwords and remove test accounts before production deployment
- **Email Verification**: All seeded accounts are pre-verified for convenience

### Testing Scenarios
- **Role-Based Access**: Test different user roles and permissions
- **Team Member Profiles**: Verify team member display and expertise
- **Client Experience**: Test consultation booking and client dashboard
- **Admin Functions**: Test content management and user administration

### Database Reset
To reset all user data and recreate these accounts:
```bash
php artisan migrate:fresh --seed
```

---

## 🔄 Account Management

### Adding New Test Users
To create additional test users, update the seeders in:
- `database/seeders/AdminUserSeeder.php` - For admin/client accounts
- `database/seeders/TeamMembersSeeder.php` - For team member accounts

### Password Reset Testing
All accounts can be used to test password reset functionality:
1. Go to `/login`
2. Click "Forgot Password"
3. Enter any of the above email addresses
4. Check mail logs for reset links

---

*Last Updated: August 19, 2025*  
*Total Accounts: 7 (1 admin + 1 client + 5 team members)*
