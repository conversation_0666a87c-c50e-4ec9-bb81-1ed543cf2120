<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\TeamMember;
use App\Models\Consultation;
use App\Models\Payment;
use App\Models\BlogPost;
use App\Models\Comment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_has_fillable_attributes(): void
    {
        $fillable = [
            'name', 'email', 'password', 'role', 'avatar', 'phone', 'company'
        ];

        $user = new User();
        $this->assertEquals($fillable, $user->getFillable());
    }

    public function test_user_role_methods(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $client = User::factory()->create(['role' => 'client']);
        $teamMember = User::factory()->create(['role' => 'team_member']);

        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($admin->isClient());
        $this->assertFalse($admin->isTeamMember());

        $this->assertFalse($client->isAdmin());
        $this->assertTrue($client->isClient());
        $this->assertFalse($client->isTeamMember());

        $this->assertFalse($teamMember->isAdmin());
        $this->assertFalse($teamMember->isClient());
        $this->assertTrue($teamMember->isTeamMember());
    }

    public function test_user_has_team_member_relationship(): void
    {
        $user = User::factory()->create(['role' => 'team_member']);
        $teamMember = TeamMember::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(TeamMember::class, $user->teamMember);
        $this->assertEquals($teamMember->id, $user->teamMember->id);
    }

    public function test_user_has_consultations_relationship(): void
    {
        $user = User::factory()->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $user->consultations);
    }

    public function test_user_has_payments_relationship(): void
    {
        $user = User::factory()->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $user->payments);
    }

    public function test_user_has_blog_posts_relationship(): void
    {
        $user = User::factory()->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $user->blogPosts);
    }

    public function test_user_has_comments_relationship(): void
    {
        $user = User::factory()->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $user->comments);
    }

    public function test_user_password_is_hashed(): void
    {
        $user = User::factory()->create(['password' => 'password']);

        $this->assertNotEquals('password', $user->password);
        $this->assertTrue(\Hash::check('password', $user->password));
    }

    public function test_user_email_verified_at_is_cast_to_datetime(): void
    {
        $user = User::factory()->create(['email_verified_at' => now()]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $user->email_verified_at);
    }
}
