<?php

namespace Tests\Unit;

use App\Models\Service;
use App\Models\Consultation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ServiceModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_service_has_fillable_attributes(): void
    {
        $fillable = [
            'title', 'slug', 'description', 'detailed_description', 'features',
            'price_range', 'category', 'is_active', 'sort_order', 'meta_title', 'meta_description'
        ];

        $service = new Service();
        $this->assertEquals($fillable, $service->getFillable());
    }

    public function test_service_auto_generates_slug_on_create(): void
    {
        $service = Service::create([
            'title' => 'Test Service Title',
            'description' => 'Test description',
            'detailed_description' => 'Test detailed description',
            'category' => 'Test Category',
        ]);

        $this->assertEquals('test-service-title', $service->slug);
    }

    public function test_service_updates_slug_when_title_changes(): void
    {
        $service = Service::create([
            'title' => 'Original Title',
            'description' => 'Test description',
            'detailed_description' => 'Test detailed description',
            'category' => 'Test Category',
        ]);
        $originalSlug = $service->slug;

        $service->update(['title' => 'New Title', 'slug' => '']);

        $this->assertNotEquals($originalSlug, $service->slug);
        $this->assertEquals('new-title', $service->slug);
    }

    public function test_service_uses_slug_as_route_key(): void
    {
        $service = new Service();

        $this->assertEquals('slug', $service->getRouteKeyName());
    }

    public function test_service_features_are_cast_to_array(): void
    {
        $features = ['Feature 1', 'Feature 2', 'Feature 3'];
        $service = Service::factory()->create(['features' => $features]);

        $this->assertIsArray($service->features);
        $this->assertEquals($features, $service->features);
    }

    public function test_service_is_active_is_cast_to_boolean(): void
    {
        $service = Service::factory()->create(['is_active' => 1]);

        $this->assertIsBool($service->is_active);
        $this->assertTrue($service->is_active);
    }

    public function test_service_active_scope(): void
    {
        Service::factory()->create(['is_active' => true]);
        Service::factory()->create(['is_active' => false]);

        $activeServices = Service::active()->get();

        $this->assertCount(1, $activeServices);
        $this->assertTrue($activeServices->first()->is_active);
    }

    public function test_service_ordered_scope(): void
    {
        Service::factory()->create(['sort_order' => 3]);
        Service::factory()->create(['sort_order' => 1]);
        Service::factory()->create(['sort_order' => 2]);

        $orderedServices = Service::ordered()->get();

        $this->assertEquals(1, $orderedServices->first()->sort_order);
        $this->assertEquals(3, $orderedServices->last()->sort_order);
    }

    public function test_service_by_category_scope(): void
    {
        Service::factory()->create(['category' => 'Meta Ads']);
        Service::factory()->create(['category' => 'Analytics']);
        Service::factory()->create(['category' => 'Meta Ads']);

        $metaAdsServices = Service::byCategory('Meta Ads')->get();

        $this->assertCount(2, $metaAdsServices);
        $metaAdsServices->each(function ($service) {
            $this->assertEquals('Meta Ads', $service->category);
        });
    }

    public function test_service_has_consultations_relationship(): void
    {
        $service = Service::factory()->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $service->consultations);
    }
}
