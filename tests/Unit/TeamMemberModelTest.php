<?php

namespace Tests\Unit;

use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TeamMemberModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_team_member_has_fillable_attributes(): void
    {
        $fillable = [
            'user_id', 'name', 'position', 'bio', 'avatar',
            'expertise', 'is_active', 'sort_order'
        ];

        $teamMember = new TeamMember();
        $this->assertEquals($fillable, $teamMember->getFillable());
    }

    public function test_team_member_expertise_is_cast_to_array(): void
    {
        $expertise = ['PHP', 'Laravel', 'React', 'JavaScript'];
        $user = User::factory()->create();
        $teamMember = TeamMember::factory()->create([
            'user_id' => $user->id,
            'expertise' => $expertise
        ]);

        $this->assertIsArray($teamMember->expertise);
        $this->assertEquals($expertise, $teamMember->expertise);
    }

    public function test_team_member_is_active_is_cast_to_boolean(): void
    {
        $user = User::factory()->create();
        $teamMember = TeamMember::factory()->create([
            'user_id' => $user->id,
            'is_active' => 1
        ]);

        $this->assertIsBool($teamMember->is_active);
        $this->assertTrue($teamMember->is_active);
    }

    public function test_team_member_active_scope(): void
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        TeamMember::factory()->create(['user_id' => $user1->id, 'is_active' => true]);
        TeamMember::factory()->create(['user_id' => $user2->id, 'is_active' => false]);

        $activeMembers = TeamMember::active()->get();

        $this->assertCount(1, $activeMembers);
        $this->assertTrue($activeMembers->first()->is_active);
    }

    public function test_team_member_ordered_scope(): void
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create();

        TeamMember::factory()->create(['user_id' => $user1->id, 'sort_order' => 3]);
        TeamMember::factory()->create(['user_id' => $user2->id, 'sort_order' => 1]);
        TeamMember::factory()->create(['user_id' => $user3->id, 'sort_order' => 2]);

        $orderedMembers = TeamMember::ordered()->get();

        $this->assertEquals(1, $orderedMembers->first()->sort_order);
        $this->assertEquals(3, $orderedMembers->last()->sort_order);
    }

    public function test_team_member_belongs_to_user(): void
    {
        $user = User::factory()->create();
        $teamMember = TeamMember::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $teamMember->user);
        $this->assertEquals($user->id, $teamMember->user->id);
    }
}
