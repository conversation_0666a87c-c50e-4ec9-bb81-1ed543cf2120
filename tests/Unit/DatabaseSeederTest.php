<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Service;
use App\Models\TeamMember;
use Database\Seeders\AdminUserSeeder;
use Database\Seeders\ServicesSeeder;
use Database\Seeders\TeamMembersSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DatabaseSeederTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_user_seeder_creates_admin_user(): void
    {
        $this->seed(AdminUserSeeder::class);

        $admin = User::where('email', '<EMAIL>')->first();

        $this->assertNotNull($admin);
        $this->assertEquals('Yearon Suraiya', $admin->name);
        $this->assertEquals('admin', $admin->role);
        $this->assertNotNull($admin->email_verified_at);
    }

    public function test_admin_user_seeder_creates_test_client(): void
    {
        $this->seed(AdminUserSeeder::class);

        $client = User::where('email', '<EMAIL>')->first();

        $this->assertNotNull($client);
        $this->assertEquals('Test Client', $client->name);
        $this->assertEquals('client', $client->role);
        $this->assertNotNull($client->email_verified_at);
    }

    public function test_services_seeder_creates_services(): void
    {
        $this->seed(ServicesSeeder::class);

        $services = Service::all();

        $this->assertCount(4, $services);

        // Check specific services exist
        $this->assertTrue($services->contains('slug', 'meta-ads-campaign-setup-management'));
        $this->assertTrue($services->contains('slug', 'facebook-pixel-conversion-tracking-setup'));
        $this->assertTrue($services->contains('slug', 'google-analytics-4-gtm-setup'));
        $this->assertTrue($services->contains('slug', 'free-strategy-consultation'));
    }

    public function test_services_seeder_creates_services_with_correct_data(): void
    {
        $this->seed(ServicesSeeder::class);

        $metaAdsService = Service::where('slug', 'meta-ads-campaign-setup-management')->first();

        $this->assertNotNull($metaAdsService);
        $this->assertEquals('Meta Ads Campaign Setup & Management', $metaAdsService->title);
        $this->assertEquals('Meta Ads', $metaAdsService->category);
        $this->assertTrue($metaAdsService->is_active);
        $this->assertIsArray($metaAdsService->features);
        $this->assertNotEmpty($metaAdsService->features);
    }

    public function test_team_members_seeder_creates_team_members(): void
    {
        $this->seed(TeamMembersSeeder::class);

        $teamMembers = TeamMember::all();
        $teamMemberUsers = User::where('role', 'team_member')->get();

        $this->assertCount(5, $teamMembers);
        $this->assertCount(5, $teamMemberUsers);
    }

    public function test_team_members_seeder_creates_correct_team_members(): void
    {
        $this->seed(TeamMembersSeeder::class);

        $expectedMembers = [
            'Jahidul Islam' => 'Google Ads Expert',
            'Rasel Sikder' => 'Google Ads & Web Analytics Expert',
            'Amit Roy' => 'Digital Marketing Specialist',
            'Sohel Ahmed Sahil' => 'Marketing Analyst',
            'Firoz Anam' => 'Full Stack Developer',
        ];

        foreach ($expectedMembers as $name => $position) {
            $teamMember = TeamMember::where('name', $name)->first();
            $this->assertNotNull($teamMember, "Team member {$name} not found");
            $this->assertEquals($position, $teamMember->position);
            $this->assertTrue($teamMember->is_active);
            $this->assertIsArray($teamMember->expertise);
            $this->assertNotEmpty($teamMember->expertise);
        }
    }

    public function test_team_members_have_user_relationships(): void
    {
        $this->seed(TeamMembersSeeder::class);

        $teamMembers = TeamMember::with('user')->get();

        foreach ($teamMembers as $teamMember) {
            $this->assertNotNull($teamMember->user);
            $this->assertEquals('team_member', $teamMember->user->role);
            $this->assertEquals($teamMember->name, $teamMember->user->name);
        }
    }
}
