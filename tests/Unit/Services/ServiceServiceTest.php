<?php

namespace Tests\Unit\Services;

use App\Models\Service;
use App\Services\ServiceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ServiceServiceTest extends TestCase
{
    use RefreshDatabase;

    private ServiceService $serviceService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->serviceService = new ServiceService();
        $this->seed();
    }

    public function test_can_get_active_services()
    {
        $services = $this->serviceService->getActiveServices(['paginate' => false]);

        $this->assertNotEmpty($services);
        foreach ($services as $service) {
            $this->assertTrue($service->is_active);
        }
    }

    public function test_can_filter_services_by_category()
    {
        $category = 'Meta Ads';
        $services = $this->serviceService->getActiveServices([
            'category' => $category,
            'paginate' => false,
        ]);

        foreach ($services as $service) {
            $this->assertEquals($category, $service->category);
        }
    }

    public function test_can_find_service_by_slug()
    {
        $service = Service::where('is_active', true)->first();
        $foundService = $this->serviceService->findBySlug($service->slug);

        $this->assertNotNull($foundService);
        $this->assertEquals($service->id, $foundService->id);
    }

    public function test_cannot_find_inactive_service_by_slug()
    {
        $service = Service::factory()->create(['is_active' => false]);
        $foundService = $this->serviceService->findBySlug($service->slug);

        $this->assertNull($foundService);
    }

    public function test_can_create_service()
    {
        $serviceData = [
            'title' => 'Test Service',
            'description' => 'Test description',
            'detailed_description' => 'Detailed test description',
            'category' => 'Test Category',
            'price_range' => '$100-$500',
            'is_active' => true,
        ];

        $service = $this->serviceService->create($serviceData);

        $this->assertInstanceOf(Service::class, $service);
        $this->assertEquals('Test Service', $service->title);
        $this->assertEquals('test-service', $service->slug);
        $this->assertDatabaseHas('services', ['title' => 'Test Service']);
    }

    public function test_auto_generates_slug_from_title()
    {
        $serviceData = [
            'title' => 'Meta Ads Campaign Management',
            'description' => 'Test description',
            'detailed_description' => 'Detailed test description',
            'category' => 'Meta Ads',
        ];

        $service = $this->serviceService->create($serviceData);

        $this->assertEquals('meta-ads-campaign-management', $service->slug);
    }

    public function test_generates_unique_slug_for_duplicate_titles()
    {
        // Create first service
        $this->serviceService->create([
            'title' => 'Duplicate Title',
            'description' => 'Test description',
            'detailed_description' => 'Detailed test description',
            'category' => 'Test',
        ]);

        // Create second service with same title
        $service2 = $this->serviceService->create([
            'title' => 'Duplicate Title',
            'description' => 'Test description',
            'detailed_description' => 'Detailed test description',
            'category' => 'Test',
        ]);

        $this->assertEquals('duplicate-title-1', $service2->slug);
    }

    public function test_can_update_service()
    {
        $service = Service::first();
        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
        ];

        $updatedService = $this->serviceService->update($service->id, $updateData);

        $this->assertEquals('Updated Title', $updatedService->title);
        $this->assertEquals('Updated description', $updatedService->description);
    }

    public function test_can_delete_service()
    {
        $service = Service::factory()->create();
        $serviceId = $service->id;

        $deleted = $this->serviceService->delete($serviceId);

        $this->assertTrue($deleted);
        $this->assertDatabaseMissing('services', ['id' => $serviceId]);
    }

    public function test_get_categories_returns_unique_categories()
    {
        $categories = $this->serviceService->getCategories();

        $this->assertIsArray($categories);
        $this->assertEquals(count($categories), count(array_unique($categories)));
    }

    public function test_search_functionality()
    {
        Service::factory()->create([
            'title' => 'Facebook Advertising',
            'description' => 'Facebook ad management',
            'is_active' => true,
        ]);

        $services = $this->serviceService->getActiveServices([
            'search' => 'Facebook',
            'paginate' => false,
        ]);

        $this->assertNotEmpty($services);
        $found = false;
        foreach ($services as $service) {
            if (str_contains($service->title, 'Facebook') || str_contains($service->description, 'Facebook')) {
                $found = true;
                break;
            }
        }
        $this->assertTrue($found);
    }

    public function test_validation_rules_for_create()
    {
        $this->expectException(\Illuminate\Validation\ValidationException::class);

        $this->serviceService->create([
            // Missing required fields
        ]);
    }

    public function test_validation_rules_for_update()
    {
        $service = Service::first();

        $this->expectException(\Illuminate\Validation\ValidationException::class);

        $this->serviceService->update($service->id, [
            'title' => '', // Invalid empty title
        ]);
    }
}
