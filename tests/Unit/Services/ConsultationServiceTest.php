<?php

namespace Tests\Unit\Services;

use App\Models\Consultation;
use App\Models\Service;
use App\Models\User;
use App\Services\ConsultationService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ConsultationServiceTest extends TestCase
{
    use RefreshDatabase;

    private ConsultationService $consultationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->consultationService = new ConsultationService();
        $this->seed();
    }

    public function test_can_book_consultation()
    {
        $user = User::where('role', 'client')->first();
        $service = Service::where('is_active', true)->first();
        
        $this->actingAs($user);

        $consultationData = [
            'service_id' => $service->id,
            'consultation_date' => Carbon::now()->addDays(1)->format('Y-m-d H:i:s'),
            'duration' => 60,
            'notes' => 'Test consultation notes',
        ];

        $consultation = $this->consultationService->bookConsultation($consultationData);

        $this->assertInstanceOf(Consultation::class, $consultation);
        $this->assertEquals($user->id, $consultation->user_id);
        $this->assertEquals($service->id, $consultation->service_id);
        $this->assertEquals('pending', $consultation->status);
        $this->assertEquals('pending', $consultation->payment_status);
    }

    public function test_cannot_book_consultation_for_inactive_service()
    {
        $user = User::where('role', 'client')->first();
        $service = Service::factory()->create(['is_active' => false]);
        
        $this->actingAs($user);

        $consultationData = [
            'service_id' => $service->id,
            'consultation_date' => Carbon::now()->addDays(1)->format('Y-m-d H:i:s'),
            'duration' => 60,
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Service not found or not available');

        $this->consultationService->bookConsultation($consultationData);
    }

    public function test_cannot_book_consultation_in_past()
    {
        $user = User::where('role', 'client')->first();
        $service = Service::where('is_active', true)->first();
        
        $this->actingAs($user);

        $consultationData = [
            'service_id' => $service->id,
            'consultation_date' => Carbon::now()->subHours(1)->format('Y-m-d H:i:s'),
            'duration' => 60,
        ];

        $this->expectException(\Illuminate\Validation\ValidationException::class);

        $this->consultationService->bookConsultation($consultationData);
    }

    public function test_can_check_time_slot_availability()
    {
        $dateTime = Carbon::now()->addDays(1)->setHour(10)->setMinute(0)->format('Y-m-d H:i:s');
        
        $isAvailable = $this->consultationService->isTimeSlotAvailable($dateTime, 60);
        
        $this->assertTrue($isAvailable);
    }

    public function test_time_slot_not_available_when_overlapping()
    {
        $user = User::where('role', 'client')->first();
        $service = Service::where('is_active', true)->first();
        
        $dateTime = Carbon::now()->addDays(1)->setHour(10)->setMinute(0);
        
        // Create existing consultation
        Consultation::factory()->create([
            'user_id' => $user->id,
            'service_id' => $service->id,
            'consultation_date' => $dateTime,
            'duration' => 60,
            'status' => 'confirmed',
        ]);

        // Check if same time slot is available
        $isAvailable = $this->consultationService->isTimeSlotAvailable(
            $dateTime->format('Y-m-d H:i:s'), 
            60
        );
        
        $this->assertFalse($isAvailable);
    }

    public function test_can_get_available_time_slots()
    {
        $date = Carbon::now()->addDays(1)->format('Y-m-d');
        
        $slots = $this->consultationService->getAvailableTimeSlots($date, 60);
        
        $this->assertIsArray($slots);
        $this->assertNotEmpty($slots);
        
        foreach ($slots as $slot) {
            $this->assertArrayHasKey('datetime', $slot);
            $this->assertArrayHasKey('formatted', $slot);
        }
    }

    public function test_can_update_consultation_status()
    {
        $consultation = Consultation::factory()->create([
            'status' => 'pending',
        ]);

        $updatedConsultation = $this->consultationService->updateStatus($consultation->id, 'confirmed');

        $this->assertEquals('confirmed', $updatedConsultation->status);
    }

    public function test_cannot_update_to_invalid_status()
    {
        $consultation = Consultation::factory()->create();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid status');

        $this->consultationService->updateStatus($consultation->id, 'invalid_status');
    }

    public function test_can_update_payment_status()
    {
        $consultation = Consultation::factory()->create([
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);

        $updatedConsultation = $this->consultationService->updatePaymentStatus(
            $consultation->id,
            'paid',
            'payment_123'
        );

        $this->assertEquals('paid', $updatedConsultation->payment_status);
        $this->assertEquals('payment_123', $updatedConsultation->payment_id);
        $this->assertEquals('confirmed', $updatedConsultation->status); // Auto-confirmed
    }

    public function test_can_get_user_consultations()
    {
        $user = User::where('role', 'client')->first();
        
        Consultation::factory()->count(3)->create([
            'user_id' => $user->id,
        ]);

        $consultations = $this->consultationService->getUserConsultations($user->id, ['paginate' => false]);

        $this->assertCount(3, $consultations);
        foreach ($consultations as $consultation) {
            $this->assertEquals($user->id, $consultation->user_id);
        }
    }

    public function test_can_filter_consultations_by_status()
    {
        $user = User::where('role', 'client')->first();
        
        Consultation::factory()->create([
            'user_id' => $user->id,
            'status' => 'confirmed',
        ]);
        
        Consultation::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        $consultations = $this->consultationService->getUserConsultations($user->id, [
            'status' => 'confirmed',
            'paginate' => false,
        ]);

        $this->assertCount(1, $consultations);
        $this->assertEquals('confirmed', $consultations->first()->status);
    }

    public function test_validation_rules_for_booking()
    {
        $user = User::where('role', 'client')->first();
        $this->actingAs($user);

        $this->expectException(\Illuminate\Validation\ValidationException::class);

        $this->consultationService->bookConsultation([
            // Missing required fields
        ]);
    }
}
