<?php

namespace Tests\Feature\Api;

use App\Models\Service;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ServiceApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_can_get_active_services()
    {
        $response = $this->getJson('/api/v1/services');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'title',
                        'slug',
                        'description',
                        'category',
                        'price_range',
                        'is_active',
                    ]
                ],
                'pagination'
            ]);
    }

    public function test_can_get_service_by_slug()
    {
        $service = Service::where('is_active', true)->first();

        $response = $this->getJson("/api/v1/services/{$service->slug}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'title',
                    'slug',
                    'description',
                    'detailed_description',
                    'features',
                    'category',
                    'price_range',
                ]
            ]);
    }

    public function test_returns_404_for_inactive_service()
    {
        $service = Service::factory()->create(['is_active' => false]);

        $response = $this->getJson("/api/v1/services/{$service->slug}");

        $response->assertStatus(404);
    }

    public function test_can_filter_services_by_category()
    {
        $category = 'Meta Ads';
        
        $response = $this->getJson("/api/v1/services?category={$category}");

        $response->assertStatus(200);
        
        $services = $response->json('data');
        foreach ($services as $service) {
            $this->assertEquals($category, $service['category']);
        }
    }

    public function test_can_search_services()
    {
        $search = 'Meta';
        
        $response = $this->getJson("/api/v1/services?search={$search}");

        $response->assertStatus(200);
    }

    public function test_admin_can_create_service()
    {
        $admin = User::where('role', 'admin')->first();
        
        $serviceData = [
            'title' => 'Test Service',
            'description' => 'Test service description',
            'detailed_description' => 'Detailed test service description',
            'category' => 'Test Category',
            'price_range' => '$100-$500',
            'features' => ['Feature 1', 'Feature 2'],
            'is_active' => true,
            'sort_order' => 1,
        ];

        $response = $this->actingAs($admin)
            ->postJson('/api/v1/admin/services', $serviceData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'title',
                    'slug',
                    'description',
                ]
            ]);

        $this->assertDatabaseHas('services', [
            'title' => 'Test Service',
            'slug' => 'test-service',
        ]);
    }

    public function test_non_admin_cannot_create_service()
    {
        $client = User::where('role', 'client')->first();
        
        $serviceData = [
            'title' => 'Test Service',
            'description' => 'Test service description',
            'category' => 'Test Category',
        ];

        $response = $this->actingAs($client)
            ->postJson('/api/v1/admin/services', $serviceData);

        $response->assertStatus(403);
    }

    public function test_admin_can_update_service()
    {
        $admin = User::where('role', 'admin')->first();
        $service = Service::first();
        
        $updateData = [
            'title' => 'Updated Service Title',
            'description' => 'Updated description',
        ];

        $response = $this->actingAs($admin)
            ->putJson("/api/v1/admin/services/{$service->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('services', [
            'id' => $service->id,
            'title' => 'Updated Service Title',
        ]);
    }

    public function test_admin_can_delete_service()
    {
        $admin = User::where('role', 'admin')->first();
        $service = Service::factory()->create();

        $response = $this->actingAs($admin)
            ->deleteJson("/api/v1/admin/services/{$service->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('services', [
            'id' => $service->id,
        ]);
    }

    public function test_service_validation_rules()
    {
        $admin = User::where('role', 'admin')->first();
        
        $response = $this->actingAs($admin)
            ->postJson('/api/v1/admin/services', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['title', 'description', 'category']);
    }
}
