<?php

namespace Tests\Feature\Api;

use App\Models\ContactSubmission;
use App\Models\NewsletterSubscription;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContactApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_can_submit_contact_form()
    {
        $contactData = [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'company' => 'Test Company',
            'subject' => 'Test Subject',
            'message' => 'This is a test message.',
        ];

        $response = $this->postJson('/api/v1/contact', $contactData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => ['id']
            ]);

        $this->assertDatabaseHas('contact_submissions', [
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
        ]);
    }

    public function test_contact_form_validation()
    {
        $response = $this->postJson('/api/v1/contact', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'email', 'subject', 'message']);
    }

    public function test_can_subscribe_to_newsletter()
    {
        $subscriptionData = [
            'email' => '<EMAIL>',
            'preferences' => ['marketing', 'updates'],
        ];

        $response = $this->postJson('/api/v1/newsletter', $subscriptionData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => ['id']
            ]);

        $this->assertDatabaseHas('newsletter_subscriptions', [
            'email' => '<EMAIL>',
            'status' => 'active',
        ]);
    }

    public function test_cannot_subscribe_duplicate_email()
    {
        NewsletterSubscription::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/newsletter', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(409);
    }

    public function test_can_reactivate_inactive_subscription()
    {
        NewsletterSubscription::factory()->inactive()->create([
            'email' => '<EMAIL>',
        ]);

        $response = $this->postJson('/api/v1/newsletter', [
            'email' => '<EMAIL>',
            'preferences' => ['updates'],
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('newsletter_subscriptions', [
            'email' => '<EMAIL>',
            'status' => 'active',
        ]);
    }

    public function test_authenticated_user_can_get_profile()
    {
        $user = User::where('role', 'client')->first();

        $response = $this->actingAs($user)
            ->getJson('/api/v1/user/profile');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'role',
                ]
            ]);
    }

    public function test_authenticated_user_can_update_profile()
    {
        $user = User::where('role', 'client')->first();

        $updateData = [
            'name' => 'Updated Name',
            'phone' => '+9876543210',
            'company' => 'Updated Company',
        ];

        $response = $this->actingAs($user)
            ->putJson('/api/v1/user/profile', $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Updated Name',
            'phone' => '+9876543210',
        ]);
    }

    public function test_unauthenticated_user_cannot_access_profile()
    {
        $response = $this->getJson('/api/v1/user/profile');

        $response->assertStatus(401);
    }

    public function test_admin_can_view_contact_submissions()
    {
        $admin = User::where('role', 'admin')->first();
        ContactSubmission::factory()->count(5)->create();

        $response = $this->actingAs($admin)
            ->getJson('/api/v1/admin/contact-submissions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'email',
                        'subject',
                        'status',
                        'created_at',
                    ]
                ],
                'pagination'
            ]);
    }

    public function test_admin_can_view_newsletter_subscriptions()
    {
        $admin = User::where('role', 'admin')->first();
        NewsletterSubscription::factory()->count(5)->create();

        $response = $this->actingAs($admin)
            ->getJson('/api/v1/admin/newsletter-subscriptions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'email',
                        'status',
                        'subscribed_at',
                    ]
                ],
                'pagination'
            ]);
    }

    public function test_non_admin_cannot_view_contact_submissions()
    {
        $client = User::where('role', 'client')->first();

        $response = $this->actingAs($client)
            ->getJson('/api/v1/admin/contact-submissions');

        $response->assertStatus(403);
    }
}
