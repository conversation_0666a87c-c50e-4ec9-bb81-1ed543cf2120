<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class DatabaseMigrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_users_table_has_correct_columns(): void
    {
        $this->assertTrue(Schema::hasTable('users'));

        $columns = [
            'id', 'name', 'email', 'email_verified_at', 'password',
            'role', 'avatar', 'phone', 'company', 'remember_token',
            'created_at', 'updated_at'
        ];

        foreach ($columns as $column) {
            $this->assertTrue(Schema::hasColumn('users', $column), "Column {$column} missing from users table");
        }
    }

    public function test_team_members_table_has_correct_columns(): void
    {
        $this->assertTrue(Schema::hasTable('team_members'));

        $columns = [
            'id', 'user_id', 'name', 'position', 'bio', 'avatar',
            'expertise', 'is_active', 'sort_order', 'created_at', 'updated_at'
        ];

        foreach ($columns as $column) {
            $this->assertTrue(Schema::hasColumn('team_members', $column), "Column {$column} missing from team_members table");
        }
    }

    public function test_services_table_has_correct_columns(): void
    {
        $this->assertTrue(Schema::hasTable('services'));

        $columns = [
            'id', 'title', 'slug', 'description', 'detailed_description',
            'features', 'price_range', 'category', 'is_active', 'sort_order',
            'meta_title', 'meta_description', 'created_at', 'updated_at'
        ];

        foreach ($columns as $column) {
            $this->assertTrue(Schema::hasColumn('services', $column), "Column {$column} missing from services table");
        }
    }

    public function test_all_required_tables_exist(): void
    {
        $requiredTables = [
            'users', 'team_members', 'services', 'categories', 'blog_posts',
            'comments', 'consultations', 'payments', 'analytics_events',
            'tracking_settings', 'contact_submissions', 'newsletter_subscriptions',
            'notifications', 'settings'
        ];

        foreach ($requiredTables as $table) {
            $this->assertTrue(Schema::hasTable($table), "Table {$table} does not exist");
        }
    }
}
