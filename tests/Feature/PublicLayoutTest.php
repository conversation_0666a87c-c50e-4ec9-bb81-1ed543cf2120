<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PublicLayoutTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that public pages use the correct layout structure.
     */
    public function test_public_pages_use_correct_layout(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        
        // Test that the response contains the expected Inertia component
        $response->assertInertia(fn ($page) => $page
            ->component('public/home')
        );
    }

    /**
     * Test that public pages are accessible to guests.
     */
    public function test_public_pages_accessible_to_guests(): void
    {
        $publicPages = [
            '/',
            '/about',
            '/services',
            '/team',
            '/blog',
            '/contact',
            '/book-consultation',
        ];

        foreach ($publicPages as $page) {
            $response = $this->get($page);
            $response->assertStatus(200);

            // Ensure it's not a redirect to login page
            $this->assertStringNotContainsString('/login', $response->headers->get('location', ''));
        }
    }

    /**
     * Test that public pages work with authenticated users.
     */
    public function test_public_pages_work_with_authenticated_users(): void
    {
        $user = \App\Models\User::factory()->create();
        
        $this->actingAs($user);

        $publicPages = [
            '/',
            '/about',
            '/services',
            '/team',
            '/blog',
            '/contact',
            '/book-consultation',
        ];

        foreach ($publicPages as $page) {
            $response = $this->get($page);
            $response->assertStatus(200);
        }
    }

    /**
     * Test that the home page redirects correctly.
     */
    public function test_home_page_routing(): void
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/home')
        );
    }

    /**
     * Test that service detail pages handle dynamic routing.
     */
    public function test_service_detail_dynamic_routing(): void
    {
        $testSlugs = [
            'meta-ads-management',
            'facebook-pixel-setup',
            'google-analytics-setup',
            'conversion-tracking',
        ];

        foreach ($testSlugs as $slug) {
            $response = $this->get("/services/{$slug}");
            
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page
                ->component('public/service-detail')
                ->where('slug', $slug)
            );
        }
    }

    /**
     * Test that blog post pages handle dynamic routing.
     */
    public function test_blog_post_dynamic_routing(): void
    {
        $testSlugs = [
            'meta-ads-best-practices',
            'facebook-pixel-guide',
            'google-analytics-setup',
            'conversion-optimization-tips',
        ];

        foreach ($testSlugs as $slug) {
            $response = $this->get("/blog/{$slug}");
            
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page
                ->component('public/blog-post')
                ->where('slug', $slug)
            );
        }
    }

    /**
     * Test that routes with special characters in slugs work.
     */
    public function test_routes_with_special_characters(): void
    {
        $specialSlugs = [
            'meta-ads-2024',
            'facebook-pixel-v2',
            'google-analytics-4',
        ];

        foreach ($specialSlugs as $slug) {
            $serviceResponse = $this->get("/services/{$slug}");
            $serviceResponse->assertStatus(200);
            
            $blogResponse = $this->get("/blog/{$slug}");
            $blogResponse->assertStatus(200);
        }
    }

    /**
     * Test that the application handles long slugs properly.
     */
    public function test_long_slug_handling(): void
    {
        $longSlug = 'this-is-a-very-long-slug-that-might-cause-issues-if-not-handled-properly-in-the-routing-system';
        
        $serviceResponse = $this->get("/services/{$longSlug}");
        $serviceResponse->assertStatus(200);
        $serviceResponse->assertInertia(fn ($page) => $page
            ->where('slug', $longSlug)
        );
        
        $blogResponse = $this->get("/blog/{$longSlug}");
        $blogResponse->assertStatus(200);
        $blogResponse->assertInertia(fn ($page) => $page
            ->where('slug', $longSlug)
        );
    }

    /**
     * Test that routes are case-sensitive.
     */
    public function test_route_case_sensitivity(): void
    {
        $response = $this->get('/ABOUT');
        // Should return 404 since routes are case-sensitive
        $response->assertStatus(404);
        
        $response = $this->get('/about');
        $response->assertStatus(200);
    }

    /**
     * Test that trailing slashes are handled correctly.
     */
    public function test_trailing_slash_handling(): void
    {
        // Test without trailing slash
        $response = $this->get('/about');
        $response->assertStatus(200);
        
        // Test with trailing slash - Laravel typically redirects
        $response = $this->get('/about/');
        // This might redirect to /about or return 404 depending on configuration
        $this->assertTrue(in_array($response->status(), [200, 301, 302, 404]));
    }
}
